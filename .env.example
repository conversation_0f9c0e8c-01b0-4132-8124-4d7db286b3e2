# Environment configuration for Universal Web Directory
# Copy this file to .env and update with your actual values

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=universal_web_directory
DB_USER=uwd_app
DB_PASSWORD=secure_password_here
DB_ECHO=false

# Crawling Configuration
CRAWL_DELAY=1
MAX_CONCURRENT_REQUESTS=10
USER_AGENT=UniversalWebDirectory/1.0 (+https://github.com/mjsmorgan/web_crawler)
RESPECT_ROBOTS_TXT=true

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/crawler.log

# Development Settings
DEBUG=false
TESTING=false
