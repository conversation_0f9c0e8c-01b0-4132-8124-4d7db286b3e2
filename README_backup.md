# Universal Web Directory - Web Crawler

A next-generation web intelligence infrastructure designed to create a comprehensive, queryable database of the entire web's structure, content, and behavioral patterns. This project represents a paradigm shift from single-purpose web crawling to comprehensive web intelligence infrastructure.

## 🎯 Project Vision

The Universal Web Directory operates on the principle of **comprehensive data collection with application-agnostic querying**. Rather than building isolated crawlers for specific applications, this system creates a centralized platform where:

- **Data collection happens once** but serves infinite use cases
- **Applications become intelligent consumers** rather than data collectors
- **Cross-application insights emerge** from shared dataset analysis
- **Infrastructure costs scale efficiently** across multiple consumers

## 🚀 Current Status

**Development Stage**: Early Prototype (≈2% complete)

The project currently includes basic web crawling and content scraping capabilities. This represents the foundational layer of what will become a comprehensive web intelligence platform.

## 📁 Project Structure

```
web_crawler/
├── README.md                           # This file
├── requirements.txt                    # Python dependencies
├── setup.py                            # Package setup configuration
├── .env.example                        # Environment variables template
├── docker-compose.yml                  # Container orchestration
├── crawler.py                          # Main CLI entry point
├── config/                             # Configuration management
│   ├── database.py                     # Database configuration
│   ├── crawler.py                      # Crawler settings
│   └── api.py                          # API configuration
├── models/                             # Database models (SQLAlchemy)
│   ├── sites.py                        # Site/domain models
│   ├── pages.py                        # Page content models
│   ├── url_frontier.py                 # URL queue management
│   └── crawl_log.py                    # Crawling activity logs
├── services/                           # Modular service architecture
│   ├── crawler/                        # Web crawling services
│   │   ├── url_discovery.py            # URLDiscoveryService
│   │   ├── content_scraper.py          # ContentScrapingService
│   │   └── legacy_migrator.py          # Migration utilities
│   ├── nlp/                            # Natural language processing
│   ├── search/                         # Search and indexing
│   └── api/                            # REST API services
├── core/                               # Core engine implementations
│   ├── url_discovery.py                # URL Discovery Engine
│   ├── content_scraper.py              # Content Scraper Engine
│   └── crawl_coordinator.py            # Crawling coordination
├── scripts/                            # Utility scripts
│   ├── setup_database.py               # Database initialization
│   ├── seed_data_loader.py             # Load directory websites
│   └── verify_setup.py                 # System verification
├── tests/                              # Test suite
├── data/                               # Data storage
│   └── seed_urls/                      # Initial crawl targets
├── database/                           # Database schema
├── monitoring/                         # System monitoring
├── deployment/                         # Deployment configurations
├── documents/                          # Design specifications
│   └── Universal Web Directory Features overall concept.txt
├── legacy_files/                       # Development history
│   ├── internet scraper. v - 0.01.txt
│   ├── web crawler ai model - v 0.01.txt
│   ├── web crawler ai model - v 0.02.txt
│   ├── web crawler ai model - v 0.03.txt
│   ├── web_crawler_ai_model_builder. -  v  0.1.txt
│   └── web_crawler_ai_model_builder. -  v  0.2.txt
└── web_crawler.code-workspace          # VS Code workspace
```

## 🛠️ Current Components

### 1. Database Layer (PostgreSQL)
- **Purpose**: Persistent storage for sites, pages, URLs, and crawl logs
- **Current Capability**: Full relational schema with 175 sites and 1,957 URLs
- **Features**: UUID primary keys, JSONB columns, comprehensive indexing
- **Status**: ✅ Operational with proper configuration

### 2. Core Engines (`core/`)
- **URL Discovery Engine**: Database-integrated URL frontier management
- **Content Scraper Engine**: Robust content extraction with metadata
- **Crawl Coordinator**: Orchestrates crawling activities
- **Status**: ✅ Fully functional with database integration

### 3. Service Architecture (`services/`)
- **URLDiscoveryService**: Service wrapper for URL discovery functionality
- **ContentScrapingService**: Service wrapper for content scraping
- **LegacyMigrator**: Migration utilities for architecture evolution
- **Status**: ✅ Migrated from legacy code with enhanced error handling

### 4. CLI Interface (`crawler.py`)
- **Purpose**: Command-line interface for all crawling operations
- **Commands**: seed, crawl, stats, sites, reset, test
- **Current Capability**: Full system control and monitoring
- **Status**: ✅ Operational with 126 seed URLs loaded

## 🔧 Technical Requirements

### Environment Setup
- **Python**: 3.12+ (recommended)
- **Environment Manager**: Anaconda/Conda
- **Dependencies**: Managed via `requirements.txt`

### Installation

1. **Create Conda Environment**:
   ```bash
   conda create --name web_crawler python=3.12 -y
   conda activate web_crawler
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Installation**:
   ```bash
   python -c "import requests, bs4; print('Dependencies installed successfully')"
   ```

### Dependencies Overview

The project uses a comprehensive `requirements.txt` file with:

- **Core Dependencies**: `requests`, `beautifulsoup4`, `lxml` for web scraping
- **Enhancement Tools**: `pydantic`, `loguru`, `ratelimit` for robust functionality
- **Development Tools**: `black`, `flake8`, `pytest` for code quality
- **Future Capabilities**: Commented sections for NLP, databases, and advanced crawling

For development with additional features, uncomment desired packages in `requirements.txt` and reinstall.

## 🚦 Current Usage

The system now provides a unified CLI interface for all operations:

### Database Setup (One-time)
```bash
conda activate web_crawler
python scripts/setup_database.py
python scripts/seed_data_loader.py
```

### Basic Crawling Operations
```bash
# Load seed URLs (126 directory websites)
python crawler.py seed

# Start crawling
python crawler.py crawl --limit 10

# View statistics
python crawler.py stats

# List discovered sites
python crawler.py sites --limit 5
```

### Advanced Operations
```bash
# Reset database (careful!)
python crawler.py reset

# Run system tests
python crawler.py test

# Verify setup
python scripts/verify_setup.py
```

**Database Integration**: All data is stored in PostgreSQL with proper indexing and relationships.

## ⚠️ Current Limitations & Next Steps

### Resolved Issues ✅
- [x] Hardcoded target URLs → Now uses 126 directory websites as seeds
- [x] No database integration → PostgreSQL with full schema
- [x] No workflow coordination → Unified CLI interface
- [x] Directory structure assumptions → Proper project organization
- [x] No configuration management → Environment-based configuration

### Remaining Limitations
- [ ] No rate limiting or politeness delays (Phase 1 priority)
- [ ] No robots.txt compliance (Phase 2 priority)
- [ ] Basic content extraction only (Phase 3 enhancement)
- [ ] No distributed crawling (Phase 5 scalability)

### Code Quality Improvements Needed
- [ ] Enhanced error handling for edge cases
- [ ] Comprehensive test coverage expansion
- [ ] Performance optimization for large-scale crawling
- [ ] API documentation with OpenAPI/Swagger

## 🗺️ Development Roadmap

### Phase 0: Environment & Architecture (✅ COMPLETED)
- [x] PostgreSQL database setup with proper configuration
- [x] All 126 directory websites loaded as seed URLs
- [x] New modular project structure implemented
- [x] Legacy code successfully migrated to services architecture
- [x] Basic tests passing
- [x] Documentation updated

### Phase 1: Foundation (Current Priority)
- [x] Create comprehensive requirements.txt file
- [x] Update project documentation
- [x] Install dependencies in conda environment
- [x] Create proper directory structure
- [x] Add configuration management
- [x] Implement basic logging
- [ ] Implement robust error handling

### Phase 2: Core Functionality
- [ ] URL validation and filtering
- [ ] Rate limiting and politeness
- [ ] Robots.txt compliance
- [ ] Enhanced content extraction
- [ ] Data deduplication

### Phase 3: Intelligence Layer
- [ ] Content classification and tagging
- [ ] NLP-based content analysis
- [ ] Metadata extraction and enrichment
- [ ] Quality scoring algorithms
- [ ] Relationship mapping

### Phase 4: Advanced Features
- [ ] Vector-based semantic search
- [ ] Real-time trend detection
- [ ] Authority scoring systems
- [ ] Predictive analytics
- [ ] API development

### Phase 5: Scale & Production
- [ ] Distributed crawling architecture
- [ ] Database integration (hybrid vector/relational)
- [ ] Performance optimization
- [ ] Monitoring and alerting
- [ ] Production deployment

## 🎯 Long-term Vision

The Universal Web Directory aims to become a comprehensive web intelligence platform supporting:

- **E-commerce Intelligence**: Product research, competitor analysis
- **Academic Research**: Source discovery, citation networks
- **Marketing & PR**: Influencer identification, content placement
- **Business Intelligence**: Market research, trend analysis
- **Content Strategy**: Gap analysis, competitive insights
- **News Aggregation**: Source diversity, authority scoring

## 🤝 Contributing

This project is in active development. Contributions are welcome!

### Getting Started
1. Fork the repository
2. Create a feature branch
3. Set up the conda environment: `conda activate web_crawler`
4. Install dependencies: `pip install -r requirements.txt`
5. Make your changes
6. Test thoroughly
7. Submit a pull request

### Development Guidelines
- Use the `web_crawler` conda environment for all development
- Install dependencies via `requirements.txt`
- Follow PEP 8 style guidelines (use `black` and `flake8`)
- Add comprehensive error handling
- Include docstrings and type hints
- Test changes before submitting (use `pytest`)
- Update documentation for new features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For questions, issues, or suggestions:
- Open an issue on GitHub
- Review the design documents in `/documents/`
- Check the development history in `/legacy_files/`

---

**Note**: This project represents an ambitious vision for web intelligence infrastructure. The current implementation is a foundational prototype that will evolve significantly as development progresses.
