#!/usr/bin/env python3
"""
Enhanced Content Scraper - Phase 0 Implementation
Replaces the basic content_scraper.py with database-integrated functionality
"""

import sys
import logging
import time
import hashlib
from urllib.parse import urlparse
from typing import Optional, Dict, Any
from pathlib import Path
from datetime import datetime

# Assuming the project is structured such that 'config' and 'models' are discoverable
# e.g., PYTHONPATH includes the project root, or the project is installed.
# The sys.path manipulation here is removed as it's better handled by the execution environment.

from config.database import get_db_session
from models import Site, Page, URLFrontier, CrawlLog
import requests
from bs4 import BeautifulSoup

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ContentScraper:
    """Enhanced content scraper with database integration"""
    
    def __init__(self, request_delay=1, timeout=30):
        self.request_delay = request_delay
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'UniversalWebDirectory/1.0 (+https://github.com/mjsmorgan/web_crawler)',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        logger.info("Content Scraper initialized")
    
    def fetch_page(self, url: str) -> Optional[Dict[str, Any]]:
        """Fetch a page and return structured content data"""
        try:
            logger.info(f"Fetching: {url}")
            
            # Add delay between requests
            time.sleep(self.request_delay)
            
            # Make request
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            # Parse content
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract structured data
            page_data = {
                'url': url,
                'status_code': response.status_code,
                'content_type': response.headers.get('content-type', ''),
                'content_length': len(response.content),
                'html_content': response.text,
                'title': self._extract_title(soup),
                'meta_description': self._extract_meta_description(soup),
                'meta_keywords': self._extract_meta_keywords(soup),
                'headings': self._extract_headings(soup),
                'links': self._extract_links(soup, url),
                'images': self._extract_images(soup, url),
                'text_content': self._extract_text_content(soup),
                'content_hash': self._calculate_content_hash(response.text),
                'response_time': response.elapsed.total_seconds(),
                'headers': dict(response.headers),
                'fetch_timestamp': datetime.utcnow()
            }
            
            logger.info(f"Successfully fetched {url} - {page_data['title'][:50]}...")
            return page_data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error fetching {url}: {e}")
            return None
    
    def _extract_title(self, soup: BeautifulSoup) -> str:
        """Extract page title"""
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.get_text().strip()
        
        # Fallback to h1
        h1_tag = soup.find('h1')
        if h1_tag:
            return h1_tag.get_text().strip()
        
        return "No title found"
    
    def _extract_meta_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract meta description"""
        meta_desc = soup.find('meta', attrs={'name': 'description'})
        if meta_desc:
            return meta_desc.get('content', '').strip()
        
        # Try property="description"
        meta_desc = soup.find('meta', attrs={'property': 'description'})
        if meta_desc:
            return meta_desc.get('content', '').strip()
        
        return None
    
    def _extract_meta_keywords(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract meta keywords"""
        meta_keywords = soup.find('meta', attrs={'name': 'keywords'})
        if meta_keywords:
            return meta_keywords.get('content', '').strip()
        return None
    
    def _extract_headings(self, soup: BeautifulSoup) -> Dict[str, list]:
        """Extract all headings (h1-h6)"""
        headings = {}
        for i in range(1, 7):
            tag_name = f'h{i}'
            tags = soup.find_all(tag_name)
            headings[tag_name] = [tag.get_text().strip() for tag in tags]
        return headings
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract all links from the page"""
        links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href')
            text = link.get_text().strip()
            title = link.get('title', '')
            
            links.append({
                'href': href,
                'text': text,
                'title': title
            })
        
        return links
    
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> list:
        """Extract all images from the page"""
        images = []
        for img in soup.find_all('img'):
            src = img.get('src')
            alt = img.get('alt', '')
            title = img.get('title', '')
            
            if src:
                images.append({
                    'src': src,
                    'alt': alt,
                    'title': title
                })
        
        return images
    
    def _extract_text_content(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from the page"""
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text and clean it up
        text = soup.get_text()
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _calculate_content_hash(self, content: str) -> str:
        """Calculate SHA-256 hash of content for duplicate detection"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def save_page_to_database(self, page_data: Dict[str, Any]) -> Optional[str]:
        """Save scraped page data to database"""
        try:
            with next(get_db_session()) as db:
                # Parse URL to get domain
                parsed = urlparse(page_data['url'])
                domain = parsed.netloc
                
                # Get site
                site = db.query(Site).filter(Site.domain == domain).first()
                if not site:
                    logger.error(f"Site not found for domain: {domain}")
                    return None
                
                # Check for existing page with same URL
                existing_page = db.query(Page).filter(
                    Page.url == page_data['url']
                ).first()
                
                if existing_page:
                    # Update existing page
                    existing_page.title = page_data['title']
                    existing_page.content_hash = page_data['content_hash']
                    existing_page.html_content = page_data['html_content']
                    existing_page.text_content = page_data['text_content']
                    existing_page.content_type = page_data['content_type']
                    existing_page.content_length = page_data['content_length']
                    existing_page.http_status = page_data['status_code']
                    existing_page.crawled_at = page_data['fetch_timestamp']
                    existing_page.word_count = len(page_data['text_content'].split()) if page_data['text_content'] else 0
                    
                    # Store metadata in a simple way for now
                    # Note: The current Page model doesn't have a metadata field
                    # We'll store key info in the existing fields
                    
                    db.commit()
                    db.refresh(existing_page)
                    
                    logger.info(f"Updated existing page: {page_data['url']}")
                    return str(existing_page.id)
                
                else:
                    # Create new page
                    new_page = Page(
                        url=page_data['url'],
                        site_id=site.id,
                        title=page_data['title'],
                        content_hash=page_data['content_hash'],
                        html_content=page_data['html_content'],
                        text_content=page_data['text_content'],
                        content_type=page_data['content_type'],
                        content_length=page_data['content_length'],
                        http_status=page_data['status_code'],
                        crawled_at=page_data['fetch_timestamp'],
                        word_count=len(page_data['text_content'].split()) if page_data['text_content'] else 0
                    )
                    
                    db.add(new_page)
                    db.commit()
                    db.refresh(new_page)
                    
                    logger.info(f"Saved new page: {page_data['url']}")
                    return str(new_page.id)
                    
        except Exception as e:
            logger.error(f"Error saving page to database: {e}")
            return None
    
    def log_crawl_activity(self, url: str, status: str, message: str = "", http_status: int = None):
        """Log crawl activity to database"""
        try:
            with next(get_db_session()) as db:
                # Parse URL to get domain
                parsed = urlparse(url)
                domain = parsed.netloc

                # Get site
                site = db.query(Site).filter(Site.domain == domain).first()
                if not site:
                    return

                # Create crawl log entry
                log_entry = CrawlLog(
                    site_id=site.id,
                    url=url,
                    status=status,
                    error_message=message if status == 'failed' else None,
                    http_status=http_status,
                    user_agent=self.session.headers.get('User-Agent')
                )

                db.add(log_entry)
                db.commit()

        except Exception as e:
            logger.error(f"Error logging crawl activity: {e}")
    
    def scrape_and_save(self, url: str) -> Optional[str]:
        """Complete scraping workflow: fetch, parse, and save"""
        try:
            # Fetch page data
            page_data = self.fetch_page(url)
            if not page_data:
                self.log_crawl_activity(url, 'failed', 'Failed to fetch page')
                return None
            
            # Save to database
            page_id = self.save_page_to_database(page_data)
            if page_id:
                self.log_crawl_activity(url, 'success', 'Page scraped successfully',
                                      page_data.get('status_code'))
                return page_id
            else:
                self.log_crawl_activity(url, 'failed', 'Failed to save page to database')
                return None

        except Exception as e:
            logger.error(f"Error in scrape_and_save for {url}: {e}")
            self.log_crawl_activity(url, 'failed', f'Unexpected error: {str(e)}')
            return None

def main():
    """Demo function"""
    scraper = ContentScraper()
    
    # Test URLs
    test_urls = [
        'https://example.com',
        'https://httpbin.org/html'
    ]
    
    for url in test_urls:
        page_id = scraper.scrape_and_save(url)
        if page_id:
            print(f"Successfully scraped {url} -> Page ID: {page_id}")
        else:
            print(f"Failed to scrape {url}")

if __name__ == "__main__":
    main()
