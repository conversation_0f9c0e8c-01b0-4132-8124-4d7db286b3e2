#!/usr/bin/env python3
"""
Basic Crawling Coordination System - Phase 0 Implementation
Coordinates URL discovery and content scraping with database integration
"""

import sys
import logging
import time
from typing import List, Optional
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.database import get_db_session
from models import Site, URLFrontier, Page, CrawlLog
from core.url_discovery import URLDiscoveryEngine
from core.content_scraper import ContentScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CrawlCoordinator:
    """Basic crawling coordination system"""
    
    def __init__(self, max_concurrent=5, crawl_delay=1, max_depth=3):
        self.max_concurrent = max_concurrent
        self.crawl_delay = crawl_delay
        self.max_depth = max_depth
        
        # Initialize components
        self.url_discovery = URLDiscoveryEngine(crawl_delay=crawl_delay)
        self.content_scraper = ContentScraper(request_delay=crawl_delay)
        
        # Crawl statistics
        self.stats = {
            'pages_crawled': 0,
            'urls_discovered': 0,
            'errors': 0,
            'start_time': None,
            'last_activity': None
        }
        
        logger.info("Crawl Coordinator initialized")
    
    def add_seed_urls(self, urls: List[str]) -> int:
        """Add multiple seed URLs to start crawling"""
        added_count = 0
        for url in urls:
            if self.url_discovery.add_seed_url(url):
                added_count += 1
        
        logger.info(f"Added {added_count} seed URLs")
        return added_count
    
    def crawl_single_url(self, url_entry: URLFrontier) -> bool:
        """Crawl a single URL and process results"""
        try:
            url = url_entry.url
            logger.info(f"Crawling URL: {url} (depth: {url_entry.depth})")
            
            # Mark URL as processing
            self.url_discovery.mark_url_processing(str(url_entry.id))
            
            # Check robots.txt compliance
            with next(get_db_session()) as db:
                site = db.query(Site).filter(Site.id == url_entry.site_id).first()
                if not site:
                    logger.error(f"Site not found for URL: {url}")
                    return False
                
                if not self.url_discovery.is_crawl_allowed(url, site):
                    logger.info(f"Crawling not allowed by robots.txt: {url}")
                    self.url_discovery.mark_url_completed(str(url_entry.id))
                    return False
            
            # Scrape the page
            page_id = self.content_scraper.scrape_and_save(url)
            if not page_id:
                logger.error(f"Failed to scrape page: {url}")
                self._mark_url_failed(url_entry.id, "Failed to scrape page")
                self.stats['errors'] += 1
                return False
            
            # Get page content for URL discovery
            with next(get_db_session()) as db:
                page = db.query(Page).filter(Page.id == page_id).first()
                if page and page.html_content and url_entry.depth < self.max_depth:
                    # Discover new URLs from this page
                    discovered_urls = self.url_discovery.discover_urls_from_page(
                        url, page.html_content, str(page.site_id), url_entry.depth + 1
                    )
                    
                    # Add discovered URLs to frontier
                    if discovered_urls:
                        added_count = self.url_discovery.add_discovered_urls(
                            discovered_urls, page_id, url_entry.depth + 1
                        )
                        self.stats['urls_discovered'] += added_count
                        logger.info(f"Added {added_count} new URLs from {url}")
            
            # Mark URL as completed
            self.url_discovery.mark_url_completed(str(url_entry.id))
            
            # Update statistics
            self.stats['pages_crawled'] += 1
            self.stats['last_activity'] = datetime.utcnow()
            
            logger.info(f"Successfully crawled: {url}")
            return True
            
        except Exception as e:
            logger.error(f"Error crawling {url_entry.url}: {e}")
            self._mark_url_failed(url_entry.id, f"Unexpected error: {str(e)}")
            self.stats['errors'] += 1
            return False
    
    def _mark_url_failed(self, url_id: str, reason: str):
        """Mark a URL as failed in the database"""
        try:
            with next(get_db_session()) as db:
                url_entry = db.query(URLFrontier).filter(URLFrontier.id == url_id).first()
                if url_entry:
                    url_entry.mark_failed(reason)
                    db.commit()
        except Exception as e:
            logger.error(f"Error marking URL as failed: {e}")
    
    def run_crawl_batch(self, batch_size: int = 10) -> dict:
        """Run a batch of crawls"""
        logger.info(f"Starting crawl batch (size: {batch_size})")
        
        if not self.stats['start_time']:
            self.stats['start_time'] = datetime.utcnow()
        
        # Get URLs to crawl
        urls_to_crawl = self.url_discovery.get_next_urls(batch_size)
        
        if not urls_to_crawl:
            logger.info("No URLs available for crawling")
            return self.get_crawl_stats()
        
        # Process each URL
        successful_crawls = 0
        for url_entry in urls_to_crawl:
            try:
                if self.crawl_single_url(url_entry):
                    successful_crawls += 1
                
                # Add delay between requests
                time.sleep(self.crawl_delay)
                
            except KeyboardInterrupt:
                logger.info("Crawl interrupted by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error in batch crawl: {e}")
                continue
        
        logger.info(f"Batch complete: {successful_crawls}/{len(urls_to_crawl)} successful")
        return self.get_crawl_stats()
    
    def run_continuous_crawl(self, max_pages: int = 100, max_time_minutes: int = 60):
        """Run continuous crawling with limits"""
        logger.info(f"Starting continuous crawl (max_pages: {max_pages}, max_time: {max_time_minutes}min)")
        
        self.stats['start_time'] = datetime.utcnow()
        end_time = self.stats['start_time'] + timedelta(minutes=max_time_minutes)
        
        try:
            while (self.stats['pages_crawled'] < max_pages and 
                   datetime.utcnow() < end_time):
                
                # Run a batch
                batch_stats = self.run_crawl_batch(batch_size=10)
                
                # Check if we have more URLs to crawl
                frontier_stats = self.url_discovery.get_frontier_stats()
                if frontier_stats.get('pending', 0) == 0:
                    logger.info("No more URLs to crawl")
                    break
                
                # Brief pause between batches
                time.sleep(5)
                
        except KeyboardInterrupt:
            logger.info("Continuous crawl interrupted by user")
        
        final_stats = self.get_crawl_stats()
        logger.info(f"Continuous crawl complete: {final_stats}")
        return final_stats
    
    def get_crawl_stats(self) -> dict:
        """Get current crawling statistics"""
        frontier_stats = self.url_discovery.get_frontier_stats()
        
        runtime = None
        if self.stats['start_time']:
            runtime = (datetime.utcnow() - self.stats['start_time']).total_seconds()
        
        return {
            'pages_crawled': self.stats['pages_crawled'],
            'urls_discovered': self.stats['urls_discovered'],
            'errors': self.stats['errors'],
            'runtime_seconds': runtime,
            'frontier_stats': frontier_stats,
            'last_activity': self.stats['last_activity']
        }
    
    def get_site_summary(self) -> List[dict]:
        """Get summary of crawled sites"""
        try:
            with next(get_db_session()) as db:
                sites = db.query(Site).all()
                summary = []
                
                for site in sites:
                    page_count = db.query(Page).filter(Page.site_id == site.id).count()
                    pending_urls = db.query(URLFrontier).filter(
                        URLFrontier.site_id == site.id,
                        URLFrontier.status == 'pending'
                    ).count()
                    
                    summary.append({
                        'domain': site.domain,
                        'title': site.title,
                        'pages_crawled': page_count,
                        'pending_urls': pending_urls,
                        'created_at': site.created_at
                    })
                
                return summary
                
        except Exception as e:
            logger.error(f"Error getting site summary: {e}")
            return []
    
    def reset_failed_urls(self) -> int:
        """Reset failed URLs to pending for retry"""
        try:
            with next(get_db_session()) as db:
                failed_urls = db.query(URLFrontier).filter(
                    URLFrontier.status == 'failed'
                ).all()
                
                count = 0
                for url_entry in failed_urls:
                    url_entry.status = 'pending'
                    url_entry.attempts = 0
                    url_entry.last_attempt = None
                    count += 1
                
                db.commit()
                logger.info(f"Reset {count} failed URLs to pending")
                return count
                
        except Exception as e:
            logger.error(f"Error resetting failed URLs: {e}")
            return 0

def main():
    """Demo function"""
    coordinator = CrawlCoordinator()
    
    # Add seed URLs
    seed_urls = [
        'https://example.com',
        'https://httpbin.org',
        'https://python.org'
    ]
    
    coordinator.add_seed_urls(seed_urls)
    
    # Run a small batch crawl
    print("Starting demo crawl...")
    stats = coordinator.run_crawl_batch(batch_size=5)
    
    print("\nCrawl Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\nSite Summary:")
    sites = coordinator.get_site_summary()
    for site in sites:
        print(f"  {site['domain']}: {site['pages_crawled']} pages")

if __name__ == "__main__":
    main()
