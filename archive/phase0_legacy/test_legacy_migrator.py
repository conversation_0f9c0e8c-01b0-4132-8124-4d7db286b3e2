import unittest
from unittest.mock import patch, MagicMock

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Import LegacyMigrator after sys.path modification
from services.crawler.legacy_migrator import LegacyMigrator

class TestLegacyMigrator(unittest.TestCase):

    def test_legacy_migrator_instantiation(self):
        """Test that LegacyMigrator can be instantiated."""
        try:
            migrator = LegacyMigrator()
            self.assertIsNotNone(migrator)
        except Exception as e:
            self.fail(f"LegacyMigrator instantiation failed: {e}")

    @patch('services.crawler.legacy_migrator.URLDiscoveryEngine')
    def test_migrate_url_discovery_success(self, MockURLDiscoveryEngine):
        """Test the migrate_url_discovery method success case."""
        # Ensure the mock is not None if the top-level import in migrator.py succeeded
        self.assertIsNotNone(MockURLDiscoveryEngine, "Mock should replace the actual class at module level.")

        mock_engine_instance = MockURLDiscoveryEngine.return_value
        mock_engine_instance.add_seed_url = MagicMock(return_value=True)
        mock_engine_instance.get_frontier_stats = MagicMock(return_value={'pending': 1, 'total_sites': 1})

        migrator = LegacyMigrator()
        # Need to re-assign the class attribute if it was None due to import failure in non-test scenario
        # For testing with patch, this makes sure migrator uses the MOCK
        migrator.URLDiscoveryEngine = MockURLDiscoveryEngine

        test_seed_urls = ['https://example.com/test']
        result = migrator.migrate_url_discovery(seed_urls=test_seed_urls)

        MockURLDiscoveryEngine.assert_called_once()
        mock_engine_instance.add_seed_url.assert_called_once_with('https://example.com/test')
        mock_engine_instance.get_frontier_stats.assert_called_once()
        self.assertTrue(result, "migrate_url_discovery should return True on success")

    @patch('services.crawler.legacy_migrator.URLDiscoveryEngine', new=None) # Simulate import failure
    def test_migrate_url_discovery_import_failure_handled(self): # Removed MockURLDiscoveryEngineIsNull argument
        """Test migrate_url_discovery when URLDiscoveryEngine is None (simulating import failure)."""
        migrator = LegacyMigrator()
         # Critical: ensure the instance's reference is also None for the test
        migrator.URLDiscoveryEngine = None

        result = migrator.migrate_url_discovery(seed_urls=['http://test.com'])
        self.assertFalse(result, "Should return False when URLDiscoveryEngine is None.")

    @patch('services.crawler.legacy_migrator.URLDiscoveryEngine')
    def test_migrate_url_discovery_general_exception(self, MockURLDiscoveryEngine):
        """Test migrate_url_discovery handling of a general exception from an engine method."""
        mock_engine_instance = MockURLDiscoveryEngine.return_value
        mock_engine_instance.get_frontier_stats.side_effect = Exception("Simulated General Error")

        migrator = LegacyMigrator()
        migrator.URLDiscoveryEngine = MockURLDiscoveryEngine

        result = migrator.migrate_url_discovery(seed_urls=['https://example.com/test_general_exception'])

        MockURLDiscoveryEngine.assert_called_once()
        mock_engine_instance.get_frontier_stats.assert_called_once()
        self.assertFalse(result, "Should return False on general exception.")

    @patch('services.crawler.legacy_migrator.ContentScraper')
    def test_migrate_content_scraper_success(self, MockContentScraper):
        """Test the migrate_content_scraper method success case."""
        self.assertIsNotNone(MockContentScraper)
        mock_scraper_instance = MockContentScraper.return_value
        mock_scraper_instance.scrape_and_save = MagicMock(return_value="mock_page_id_123")

        migrator = LegacyMigrator()
        migrator.ContentScraper = MockContentScraper

        test_urls_to_scrape = ['https://example.com/scrape_test']
        result = migrator.migrate_content_scraper(urls_to_scrape=test_urls_to_scrape)

        MockContentScraper.assert_called_once()
        mock_scraper_instance.scrape_and_save.assert_called_once_with('https://example.com/scrape_test')
        self.assertTrue(result, "migrate_content_scraper should return True on success")

    @patch('services.crawler.legacy_migrator.ContentScraper', new=None) # Simulate import failure
    def test_migrate_content_scraper_import_failure_handled(self): # Removed MockContentScraperIsNull argument
        """Test migrate_content_scraper when ContentScraper is None."""
        migrator = LegacyMigrator()
        migrator.ContentScraper = None

        result = migrator.migrate_content_scraper(urls_to_scrape=['http://test.com'])
        self.assertFalse(result, "Should return False when ContentScraper is None.")

    @patch('services.crawler.legacy_migrator.ContentScraper')
    def test_migrate_content_scraper_general_exception(self, MockContentScraper):
        """Test migrate_content_scraper handling of a general exception."""
        mock_scraper_instance = MockContentScraper.return_value
        mock_scraper_instance.scrape_and_save.side_effect = Exception("Simulated General Error")

        migrator = LegacyMigrator()
        migrator.ContentScraper = MockContentScraper

        result = migrator.migrate_content_scraper(urls_to_scrape=['https://example.com/test_general_exception'])

        MockContentScraper.assert_called_once()
        mock_scraper_instance.scrape_and_save.assert_called_once()
        self.assertFalse(result, "Should return False on general exception.")

if __name__ == '__main__':
    unittest.main()
