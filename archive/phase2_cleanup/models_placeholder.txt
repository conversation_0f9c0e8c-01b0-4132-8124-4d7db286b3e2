# This directory is intended for storing data related to trained machine learning models,
# or perhaps data dumps used for model training/evaluation, or serialized model files.
# This is distinct from the top-level 'models/' directory which contains Python code for SQLAlchemy models.
#
# Examples:
# - nlp_topic_classifier_v1.pkl: A pickled scikit-learn model.
# - entity_recognizer_tf_saved_model/: A TensorFlow SavedModel directory.
# - word_embeddings.vec: Pre-trained word embeddings.
# - training_dataset_for_summarizer.csv: Data used to train a summarization model.
