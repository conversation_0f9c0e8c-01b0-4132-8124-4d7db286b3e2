{"errors": [], "generated_at": "2025-07-01T14:39:30Z", "metrics": {"./archive/phase0_legacy/content_scraper.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 245, "nosec": 0, "skipped_tests": 0}, "./archive/phase0_legacy/crawl_coordinator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 216, "nosec": 0, "skipped_tests": 0}, "./archive/phase0_legacy/crawler.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 142, "nosec": 0, "skipped_tests": 0}, "./archive/phase0_legacy/test_legacy_migrator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 81, "nosec": 0, "skipped_tests": 0}, "./archive/phase0_legacy/test_phase0_implementation.py": {"CONFIDENCE.HIGH": 50, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 50, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 237, "nosec": 0, "skipped_tests": 0}, "./archive/phase0_legacy/url_discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 214, "nosec": 0, "skipped_tests": 0}, "./config/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./config/api.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 7, "nosec": 0, "skipped_tests": 0}, "./config/crawler.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "./config/database.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 56, "nosec": 0, "skipped_tests": 0}, "./config/nlp.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 6, "nosec": 0, "skipped_tests": 0}, "./core/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./migrations/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./models/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "./models/base.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 9, "nosec": 0, "skipped_tests": 0}, "./models/content_analysis.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 52, "nosec": 0, "skipped_tests": 0}, "./models/crawl_log.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 53, "nosec": 0, "skipped_tests": 0}, "./models/domain_policies.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 24, "nosec": 0, "skipped_tests": 0}, "./models/entities.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 64, "nosec": 0, "skipped_tests": 0}, "./models/link.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 33, "nosec": 0, "skipped_tests": 0}, "./models/page_vectors.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 39, "nosec": 0, "skipped_tests": 0}, "./models/pages.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 61, "nosec": 0, "skipped_tests": 0}, "./models/rate_limit_tracking.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 25, "nosec": 0, "skipped_tests": 0}, "./models/relationships.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 12, "nosec": 0, "skipped_tests": 0}, "./models/request_history.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 34, "nosec": 0, "skipped_tests": 0}, "./models/robots_cache.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 23, "nosec": 0, "skipped_tests": 0}, "./models/sites.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 45, "nosec": 0, "skipped_tests": 0}, "./models/url_frontier.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 83, "nosec": 0, "skipped_tests": 0}, "./scripts/data_quality_checker.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 130, "nosec": 0, "skipped_tests": 0}, "./scripts/database_migration.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 4, "nosec": 0, "skipped_tests": 0}, "./scripts/performance_monitor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 62, "nosec": 0, "skipped_tests": 0}, "./scripts/seed_data_loader.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 66, "nosec": 0, "skipped_tests": 0}, "./scripts/setup_database.py": {"CONFIDENCE.HIGH": 6, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 6, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 175, "nosec": 0, "skipped_tests": 0}, "./scripts/test_enhanced_scraper_integration.py": {"CONFIDENCE.HIGH": 5, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 5, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 115, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_1_1.py": {"CONFIDENCE.HIGH": 29, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 29, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 277, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_1_2.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 190, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_1_3.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 249, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_1_4.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 339, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_1_5.py": {"CONFIDENCE.HIGH": 17, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 17, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 201, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_2_1.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 219, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_2_2.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 282, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_2_3.py": {"CONFIDENCE.HIGH": 6, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 6, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 188, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_milestone_3_1.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 181, "nosec": 0, "skipped_tests": 0}, "./scripts/verify_setup.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 1, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 1, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 149, "nosec": 0, "skipped_tests": 0}, "./services/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./services/api/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./services/api/main.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 22, "nosec": 0, "skipped_tests": 0}, "./services/crawler/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 16, "nosec": 0, "skipped_tests": 0}, "./services/crawler/content_scraper.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 170, "nosec": 0, "skipped_tests": 0}, "./services/crawler/legacy_migrator.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 107, "nosec": 0, "skipped_tests": 0}, "./services/crawler/rate_limiter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 203, "nosec": 0, "skipped_tests": 0}, "./services/crawler/robots_handler.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 113, "nosec": 0, "skipped_tests": 0}, "./services/crawler/url_discovery.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 61, "nosec": 0, "skipped_tests": 0}, "./services/crawler/url_frontier_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 261, "nosec": 0, "skipped_tests": 0}, "./services/nlp/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./services/nlp/content_analyzer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 228, "nosec": 0, "skipped_tests": 0}, "./services/nlp/entity_extractor.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 240, "nosec": 0, "skipped_tests": 0}, "./services/nlp/entity_integration_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 207, "nosec": 0, "skipped_tests": 0}, "./services/nlp/nlp_integration_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 212, "nosec": 0, "skipped_tests": 0}, "./services/nlp/summarizer.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 21, "nosec": 0, "skipped_tests": 0}, "./services/nlp/topic_classifier.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 20, "nosec": 0, "skipped_tests": 0}, "./services/search/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./services/search/embedding_service.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 1, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 1, "SEVERITY.UNDEFINED": 0, "loc": 180, "nosec": 0, "skipped_tests": 0}, "./services/search/hybrid_search.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 54, "nosec": 0, "skipped_tests": 0}, "./services/search/structured_search.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 35, "nosec": 0, "skipped_tests": 0}, "./services/search/vector_search.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 32, "nosec": 0, "skipped_tests": 0}, "./setup.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 1, "nosec": 0, "skipped_tests": 0}, "./tests/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./tests/conftest.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 72, "nosec": 0, "skipped_tests": 0}, "./tests/test_content_scraper.py": {"CONFIDENCE.HIGH": 7, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 7, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 27, "nosec": 0, "skipped_tests": 0}, "./tests/test_database_setup.py": {"CONFIDENCE.HIGH": 25, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 25, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 91, "nosec": 0, "skipped_tests": 0}, "./tests/test_nlp_analyzer.py": {"CONFIDENCE.HIGH": 13, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 13, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 44, "nosec": 0, "skipped_tests": 0}, "./tests/unit/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./tests/unit/crawler/__init__.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 0, "nosec": 0, "skipped_tests": 0}, "./tests/unit/crawler/test_rate_limiter.py": {"CONFIDENCE.HIGH": 0, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 0, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 187, "nosec": 0, "skipped_tests": 0}, "./tests/unit/nlp/test_entity_extractor.py": {"CONFIDENCE.HIGH": 46, "CONFIDENCE.LOW": 0, "CONFIDENCE.MEDIUM": 0, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 46, "SEVERITY.MEDIUM": 0, "SEVERITY.UNDEFINED": 0, "loc": 406, "nosec": 0, "skipped_tests": 0}, "_totals": {"CONFIDENCE.HIGH": 204, "CONFIDENCE.LOW": 2, "CONFIDENCE.MEDIUM": 3, "CONFIDENCE.UNDEFINED": 0, "SEVERITY.HIGH": 0, "SEVERITY.LOW": 206, "SEVERITY.MEDIUM": 3, "SEVERITY.UNDEFINED": 0, "loc": 7641, "nosec": 0, "skipped_tests": 0}}, "results": [{"code": "25         engine = URLDiscoveryEngine()\n26         assert engine.respect_robots == True\n27         assert engine.crawl_delay == 1\n", "col_offset": 8, "end_col_offset": 44, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 26, "line_range": [26], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "26         assert engine.respect_robots == True\n27         assert engine.crawl_delay == 1\n28         assert engine.session is not None\n", "col_offset": 8, "end_col_offset": 38, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 27, "line_range": [27], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "27         assert engine.crawl_delay == 1\n28         assert engine.session is not None\n29     \n", "col_offset": 8, "end_col_offset": 41, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 28, "line_range": [28], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "38             result = engine.add_seed_url('https://example.com')\n39             assert result == True\n40             \n", "col_offset": 12, "end_col_offset": 33, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 39, "line_range": [39], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "42             site = test_session.query(Site).filter(Site.domain == 'example.com').first()\n43             assert site is not None\n44             assert site.domain == 'example.com'\n", "col_offset": 12, "end_col_offset": 35, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 43, "line_range": [43], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "43             assert site is not None\n44             assert site.domain == 'example.com'\n45             \n", "col_offset": 12, "end_col_offset": 47, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 44, "line_range": [44], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "47             url_entry = test_session.query(URLFrontier).filter(URLFrontier.url == 'https://example.com').first()\n48             assert url_entry is not None\n49             assert url_entry.status == 'pending'\n", "col_offset": 12, "end_col_offset": 40, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 48, "line_range": [48], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "48             assert url_entry is not None\n49             assert url_entry.status == 'pending'\n50     \n", "col_offset": 12, "end_col_offset": 48, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 49, "line_range": [49], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "70         # Should find HTTP(S) URLs only\n71         assert len(urls) >= 2\n72         assert 'https://example.com/page1' in urls\n", "col_offset": 8, "end_col_offset": 29, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 71, "line_range": [71], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "71         assert len(urls) >= 2\n72         assert 'https://example.com/page1' in urls\n73         assert 'https://example.com/page2' in urls  # Relative URL converted to absolute\n", "col_offset": 8, "end_col_offset": 50, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 72, "line_range": [72], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "72         assert 'https://example.com/page1' in urls\n73         assert 'https://example.com/page2' in urls  # Relative URL converted to absolute\n74         assert 'mailto:<EMAIL>' not in urls  # Email links excluded\n", "col_offset": 8, "end_col_offset": 50, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 73, "line_range": [73], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "73         assert 'https://example.com/page2' in urls  # Relative URL converted to absolute\n74         assert 'mailto:<EMAIL>' not in urls  # Email links excluded\n75         assert 'javascript:void(0)' not in urls  # JS links excluded\n", "col_offset": 8, "end_col_offset": 52, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 74, "line_range": [74], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "74         assert 'mailto:<EMAIL>' not in urls  # Email links excluded\n75         assert 'javascript:void(0)' not in urls  # JS links excluded\n76 \n", "col_offset": 8, "end_col_offset": 47, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 75, "line_range": [75], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "82         scraper = ContentScraper()\n83         assert scraper.request_delay == 1\n84         assert scraper.timeout == 30\n", "col_offset": 8, "end_col_offset": 41, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 83, "line_range": [83], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "83         assert scraper.request_delay == 1\n84         assert scraper.timeout == 30\n85         assert scraper.session is not None\n", "col_offset": 8, "end_col_offset": 36, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 84, "line_range": [84], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "84         assert scraper.timeout == 30\n85         assert scraper.session is not None\n86     \n", "col_offset": 8, "end_col_offset": 42, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 85, "line_range": [85], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "96         title = scraper._extract_title(soup)\n97         assert title == \"Test Page\"\n98         \n", "col_offset": 8, "end_col_offset": 35, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 97, "line_range": [97], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "102         title = scraper._extract_title(soup)\n103         assert title == \"Header Title\"\n104         \n", "col_offset": 8, "end_col_offset": 38, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 103, "line_range": [103], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "108         title = scraper._extract_title(soup)\n109         assert title == \"No title found\"\n110     \n", "col_offset": 8, "end_col_offset": 40, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 109, "line_range": [109], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "120         desc = scraper._extract_meta_description(soup)\n121         assert desc == \"Test description\"\n122         \n", "col_offset": 8, "end_col_offset": 41, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 121, "line_range": [121], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "126         desc = scraper._extract_meta_description(soup)\n127         assert desc is None\n128     \n", "col_offset": 8, "end_col_offset": 27, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 127, "line_range": [127], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "147         \n148         assert len(headings['h1']) == 1\n149         assert headings['h1'][0] == \"Main Title\"\n", "col_offset": 8, "end_col_offset": 39, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 148, "line_range": [148], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "148         assert len(headings['h1']) == 1\n149         assert headings['h1'][0] == \"Main Title\"\n150         assert len(headings['h2']) == 2\n", "col_offset": 8, "end_col_offset": 48, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 149, "line_range": [149], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "149         assert headings['h1'][0] == \"Main Title\"\n150         assert len(headings['h2']) == 2\n151         assert \"Subtitle 1\" in headings['h2']\n", "col_offset": 8, "end_col_offset": 39, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 150, "line_range": [150], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "150         assert len(headings['h2']) == 2\n151         assert \"Subtitle 1\" in headings['h2']\n152         assert \"Subtitle 2\" in headings['h2']\n", "col_offset": 8, "end_col_offset": 45, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 151, "line_range": [151], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "151         assert \"Subtitle 1\" in headings['h2']\n152         assert \"Subtitle 2\" in headings['h2']\n153         assert len(headings['h3']) == 1\n", "col_offset": 8, "end_col_offset": 45, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 152, "line_range": [152], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "152         assert \"Subtitle 2\" in headings['h2']\n153         assert len(headings['h3']) == 1\n154         assert headings['h3'][0] == \"Sub-subtitle\"\n", "col_offset": 8, "end_col_offset": 39, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 153, "line_range": [153], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "153         assert len(headings['h3']) == 1\n154         assert headings['h3'][0] == \"Sub-subtitle\"\n155     \n", "col_offset": 8, "end_col_offset": 50, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 154, "line_range": [154], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "167         \n168         assert hash1 == hash2  # Same content should have same hash\n169         assert hash1 != hash3  # Different content should have different hash\n", "col_offset": 8, "end_col_offset": 29, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 168, "line_range": [168], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "168         assert hash1 == hash2  # Same content should have same hash\n169         assert hash1 != hash3  # Different content should have different hash\n170         assert len(hash1) == 64  # SHA-256 hash should be 64 characters\n", "col_offset": 8, "end_col_offset": 29, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 169, "line_range": [169], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "169         assert hash1 != hash3  # Different content should have different hash\n170         assert len(hash1) == 64  # SHA-256 hash should be 64 characters\n171 \n", "col_offset": 8, "end_col_offset": 31, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 170, "line_range": [170], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "177         coordinator = CrawlCoordinator()\n178         assert coordinator.max_concurrent == 5\n179         assert coordinator.crawl_delay == 1\n", "col_offset": 8, "end_col_offset": 46, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 178, "line_range": [178], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "178         assert coordinator.max_concurrent == 5\n179         assert coordinator.crawl_delay == 1\n180         assert coordinator.max_depth == 3\n", "col_offset": 8, "end_col_offset": 43, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 179, "line_range": [179], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "179         assert coordinator.crawl_delay == 1\n180         assert coordinator.max_depth == 3\n181         assert coordinator.url_discovery is not None\n", "col_offset": 8, "end_col_offset": 41, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 180, "line_range": [180], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "180         assert coordinator.max_depth == 3\n181         assert coordinator.url_discovery is not None\n182         assert coordinator.content_scraper is not None\n", "col_offset": 8, "end_col_offset": 52, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 181, "line_range": [181], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "181         assert coordinator.url_discovery is not None\n182         assert coordinator.content_scraper is not None\n183         assert coordinator.stats['pages_crawled'] == 0\n", "col_offset": 8, "end_col_offset": 54, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 182, "line_range": [182], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "182         assert coordinator.content_scraper is not None\n183         assert coordinator.stats['pages_crawled'] == 0\n184     \n", "col_offset": 8, "end_col_offset": 54, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 183, "line_range": [183], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "195             \n196             assert added_count == 2\n197             assert mock_add.call_count == 2\n", "col_offset": 12, "end_col_offset": 35, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 196, "line_range": [196], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "196             assert added_count == 2\n197             assert mock_add.call_count == 2\n198     \n", "col_offset": 12, "end_col_offset": 43, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 197, "line_range": [197], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "213             \n214             assert 'pages_crawled' in stats\n215             assert 'urls_discovered' in stats\n", "col_offset": 12, "end_col_offset": 43, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 214, "line_range": [214], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "214             assert 'pages_crawled' in stats\n215             assert 'urls_discovered' in stats\n216             assert 'errors' in stats\n", "col_offset": 12, "end_col_offset": 45, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 215, "line_range": [215], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "215             assert 'urls_discovered' in stats\n216             assert 'errors' in stats\n217             assert 'frontier_stats' in stats\n", "col_offset": 12, "end_col_offset": 36, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 216, "line_range": [216], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "216             assert 'errors' in stats\n217             assert 'frontier_stats' in stats\n218             assert stats['frontier_stats']['pending'] == 10\n", "col_offset": 12, "end_col_offset": 44, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 217, "line_range": [217], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "217             assert 'frontier_stats' in stats\n218             assert stats['frontier_stats']['pending'] == 10\n219 \n", "col_offset": 12, "end_col_offset": 59, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 218, "line_range": [218], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "261                 site = test_session.query(Site).filter(Site.domain == 'example.com').first()\n262                 assert site is not None\n263                 \n", "col_offset": 16, "end_col_offset": 39, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 262, "line_range": [262], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "267                 ).first()\n268                 assert url_entry is not None\n269                 assert url_entry.status == 'pending'\n", "col_offset": 16, "end_col_offset": 44, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 268, "line_range": [268], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "268                 assert url_entry is not None\n269                 assert url_entry.status == 'pending'\n270 \n", "col_offset": 16, "end_col_offset": 52, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "313     # Verify relationships\n314     assert site.pages[0].id == page.id\n315     assert page.site.domain == 'example.com'\n", "col_offset": 4, "end_col_offset": 38, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 314, "line_range": [314], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "314     assert site.pages[0].id == page.id\n315     assert page.site.domain == 'example.com'\n316     assert url_entry.site.domain == 'example.com'\n", "col_offset": 4, "end_col_offset": 44, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 315, "line_range": [315], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "315     assert page.site.domain == 'example.com'\n316     assert url_entry.site.domain == 'example.com'\n", "col_offset": 4, "end_col_offset": 49, "filename": "./archive/phase0_legacy/test_phase0_implementation.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 316, "line_range": [316], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "5 API_CONFIG = {\n6     \"HOST\": \"0.0.0.0\",\n7     \"PORT\": 8000,\n8     \"DEFAULT_RATE_LIMIT\": \"100/minute\",\n9     \"ENABLE_AUTH\": True,\n10 }\n11 \n12 pass\n", "col_offset": 12, "end_col_offset": 21, "filename": "./config/api.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 605, "link": "https://cwe.mitre.org/data/definitions/605.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible binding to all interfaces.", "line_number": 6, "line_range": [5, 6, 7, 8, 9, 10], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b104_hardcoded_bind_all_interfaces.html", "test_id": "B104", "test_name": "hardcoded_bind_all_interfaces"}, {"code": "28         \"\"\"Validate database connection parameters\"\"\"\n29         if not self.password or self.password == \"secure_password_here\":\n30             logger.warning(\"Using default password. Please set DB_PASSWORD environment variable.\")\n", "col_offset": 49, "end_col_offset": 71, "filename": "./config/database.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'secure_password_here'", "line_number": 29, "line_range": [29], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "16 from models import Base, Site, URLFrontier, Page, Link, CrawlLog\n17 import subprocess\n18 \n", "col_offset": 0, "end_col_offset": 17, "filename": "./scripts/setup_database.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 17, "line_range": [17], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "43 \n44         result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)\n45 \n", "col_offset": 17, "end_col_offset": 80, "filename": "./scripts/setup_database.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 44, "line_range": [44], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "78 \n79         result = subprocess.run(create_db_cmd, capture_output=True, text=True)\n80         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 78, "filename": "./scripts/setup_database.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 79, "line_range": [79], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "99 \n100         result = subprocess.run(create_user_cmd, capture_output=True, text=True)\n101         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 80, "filename": "./scripts/setup_database.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 100, "line_range": [100], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "120 \n121         result = subprocess.run(grant_cmd, capture_output=True, text=True)\n122         if result.returncode == 0:\n", "col_offset": 17, "end_col_offset": 74, "filename": "./scripts/setup_database.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 121, "line_range": [121], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "156 \n157         result = subprocess.run(cmd, capture_output=True, text=True, env=env)\n158 \n", "col_offset": 17, "end_col_offset": 77, "filename": "./scripts/setup_database.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 157, "line_range": [157], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "123         stored_page = session.query(Page).filter_by(id=page.id).first()\n124         assert stored_page is not None, \"Page should be stored in database\"\n125         assert stored_page.title == result[\"title\"], \"Title should match\"\n", "col_offset": 8, "end_col_offset": 75, "filename": "./scripts/test_enhanced_scraper_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 124, "line_range": [124], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "124         assert stored_page is not None, \"Page should be stored in database\"\n125         assert stored_page.title == result[\"title\"], \"Title should match\"\n126         assert stored_page.content_hash == result[\"content_hash\"], \"Content hash should match\"\n", "col_offset": 8, "end_col_offset": 73, "filename": "./scripts/test_enhanced_scraper_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 125, "line_range": [125], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "125         assert stored_page.title == result[\"title\"], \"Title should match\"\n126         assert stored_page.content_hash == result[\"content_hash\"], \"Content hash should match\"\n127         assert stored_page.word_count == result[\"word_count\"], \"Word count should match\"\n", "col_offset": 8, "end_col_offset": 94, "filename": "./scripts/test_enhanced_scraper_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 126, "line_range": [126], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "126         assert stored_page.content_hash == result[\"content_hash\"], \"Content hash should match\"\n127         assert stored_page.word_count == result[\"word_count\"], \"Word count should match\"\n128         assert len(stored_page.heading_h1) == len(result[\"heading_h1\"]), \"H1 headings should match\"\n", "col_offset": 8, "end_col_offset": 88, "filename": "./scripts/test_enhanced_scraper_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 127, "line_range": [127], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "127         assert stored_page.word_count == result[\"word_count\"], \"Word count should match\"\n128         assert len(stored_page.heading_h1) == len(result[\"heading_h1\"]), \"H1 headings should match\"\n129 \n", "col_offset": 8, "end_col_offset": 99, "filename": "./scripts/test_enhanced_scraper_integration.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 128, "line_range": [128], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "131         # Verify URL parsing\n132         assert url_entry.url == test_url\n133         assert url_entry.url_hash is not None and len(url_entry.url_hash) == 64\n", "col_offset": 8, "end_col_offset": 40, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 132, "line_range": [132], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "132         assert url_entry.url == test_url\n133         assert url_entry.url_hash is not None and len(url_entry.url_hash) == 64\n134         assert url_entry.domain == \"example.com\"\n", "col_offset": 8, "end_col_offset": 79, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 133, "line_range": [133], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "133         assert url_entry.url_hash is not None and len(url_entry.url_hash) == 64\n134         assert url_entry.domain == \"example.com\"\n135         assert url_entry.path == \"/path/to/page\"\n", "col_offset": 8, "end_col_offset": 48, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 134, "line_range": [134], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "134         assert url_entry.domain == \"example.com\"\n135         assert url_entry.path == \"/path/to/page\"\n136         assert url_entry.query_params == {\"param1\": [\"value1\"], \"param2\": [\"value2\"]}\n", "col_offset": 8, "end_col_offset": 48, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 135, "line_range": [135], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "135         assert url_entry.path == \"/path/to/page\"\n136         assert url_entry.query_params == {\"param1\": [\"value1\"], \"param2\": [\"value2\"]}\n137         assert url_entry.priority == 8\n", "col_offset": 8, "end_col_offset": 85, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 136, "line_range": [136], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "136         assert url_entry.query_params == {\"param1\": [\"value1\"], \"param2\": [\"value2\"]}\n137         assert url_entry.priority == 8\n138         assert url_entry.source_type == \"test\"\n", "col_offset": 8, "end_col_offset": 38, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 137, "line_range": [137], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "137         assert url_entry.priority == 8\n138         assert url_entry.source_type == \"test\"\n139 \n", "col_offset": 8, "end_col_offset": 46, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 138, "line_range": [138], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "165             result = service.add_urls_batch(test_urls)\n166             assert result[\"added\"] == 3\n167             assert result[\"failed\"] == 0\n", "col_offset": 12, "end_col_offset": 39, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 166, "line_range": [166], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "166             assert result[\"added\"] == 3\n167             assert result[\"failed\"] == 0\n168 \n", "col_offset": 12, "end_col_offset": 40, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 167, "line_range": [167], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "170             stats = service.get_frontier_stats()\n171             assert \"total_urls\" in stats\n172             assert \"ready_for_crawl\" in stats\n", "col_offset": 12, "end_col_offset": 40, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 171, "line_range": [171], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "171             assert \"total_urls\" in stats\n172             assert \"ready_for_crawl\" in stats\n173             assert \"status_distribution\" in stats\n", "col_offset": 12, "end_col_offset": 45, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 172, "line_range": [172], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "172             assert \"ready_for_crawl\" in stats\n173             assert \"status_distribution\" in stats\n174             assert stats[\"total_urls\"] > 0\n", "col_offset": 12, "end_col_offset": 49, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 173, "line_range": [173], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "173             assert \"status_distribution\" in stats\n174             assert stats[\"total_urls\"] > 0\n175 \n", "col_offset": 12, "end_col_offset": 42, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 174, "line_range": [174], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "177             next_urls = service.get_next_urls(limit=2, worker_id=\"verification_worker\")\n178             assert len(next_urls) <= 2\n179 \n", "col_offset": 12, "end_col_offset": 38, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 178, "line_range": [178], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "189                 )\n190                 assert success is True\n191 \n", "col_offset": 16, "end_col_offset": 38, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 190, "line_range": [190], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "193             cleaned = service.cleanup_stale_workers(timeout_hours=0.01)\n194             assert isinstance(cleaned, int)\n195 \n", "col_offset": 12, "end_col_offset": 43, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 194, "line_range": [194], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "229 \n230             assert len(overlap) == 0, f\"Workers got overlapping URLs: {overlap}\"\n231 \n", "col_offset": 12, "end_col_offset": 80, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 230, "line_range": [230], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "233             for url in worker1_urls:\n234                 assert url.worker_id == \"worker_1\"\n235                 assert url.status == \"crawling\"\n", "col_offset": 16, "end_col_offset": 50, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 234, "line_range": [234], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "234                 assert url.worker_id == \"worker_1\"\n235                 assert url.status == \"crawling\"\n236 \n", "col_offset": 16, "end_col_offset": 47, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 235, "line_range": [235], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "237             for url in worker2_urls:\n238                 assert url.worker_id == \"worker_2\"\n239                 assert url.status == \"crawling\"\n", "col_offset": 16, "end_col_offset": 50, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 238, "line_range": [238], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "238                 assert url.worker_id == \"worker_2\"\n239                 assert url.status == \"crawling\"\n240 \n", "col_offset": 16, "end_col_offset": 47, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 239, "line_range": [239], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "259             success1 = service.add_url(test_url, priority=5, source_type=\"dedup_test\")\n260             assert success1 is True\n261 \n", "col_offset": 12, "end_col_offset": 35, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 260, "line_range": [260], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "263             success2 = service.add_url(test_url, priority=5, source_type=\"dedup_test\")\n264             assert success2 is False\n265 \n", "col_offset": 12, "end_col_offset": 36, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 264, "line_range": [264], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "268             url_entry2 = URLFrontier(url=test_url)\n269             assert url_entry1.url_hash == url_entry2.url_hash\n270 \n", "col_offset": 12, "end_col_offset": 61, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 269, "line_range": [269], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "290             urls = service.get_next_urls(limit=1, worker_id=\"error_worker\")\n291             assert len(urls) == 1\n292 \n", "col_offset": 12, "end_col_offset": 33, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 291, "line_range": [291], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "301             )\n302             assert success is True\n303 \n", "col_offset": 12, "end_col_offset": 34, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 302, "line_range": [302], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "306                 updated_entry = session.query(URLFrontier).filter_by(id=url_entry.id).first()\n307                 assert updated_entry.status == \"pending\"  # Should be retried\n308                 assert updated_entry.error_message == \"Connection timeout\"\n", "col_offset": 16, "end_col_offset": 56, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 307, "line_range": [307], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "307                 assert updated_entry.status == \"pending\"  # Should be retried\n308                 assert updated_entry.error_message == \"Connection timeout\"\n309                 assert updated_entry.error_type == \"network_error\"\n", "col_offset": 16, "end_col_offset": 74, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 308, "line_range": [308], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "308                 assert updated_entry.error_message == \"Connection timeout\"\n309                 assert updated_entry.error_type == \"network_error\"\n310 \n", "col_offset": 16, "end_col_offset": 66, "filename": "./scripts/verify_milestone_1_1.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 309, "line_range": [309], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "59             # Check class exists\n60             assert hasattr(self.content_scraper, \"scrape_page\"), \"scrape_page method missing\"\n61             assert hasattr(\n", "col_offset": 12, "end_col_offset": 93, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 60, "line_range": [60], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "60             assert hasattr(self.content_scraper, \"scrape_page\"), \"scrape_page method missing\"\n61             assert hasattr(\n62                 self.content_scraper, \"_extract_page_data\"\n63             ), \"_extract_page_data method missing\"\n64 \n", "col_offset": 12, "end_col_offset": 50, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 61, "line_range": [61, 62, 63], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "65             # Check initialization parameters\n66             assert hasattr(self.content_scraper, \"rate_limiter\"), \"rate_limiter not initialized\"\n67             assert hasattr(self.content_scraper, \"robots_handler\"), \"robots_handler not initialized\"\n", "col_offset": 12, "end_col_offset": 96, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 66, "line_range": [66], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "66             assert hasattr(self.content_scraper, \"rate_limiter\"), \"rate_limiter not initialized\"\n67             assert hasattr(self.content_scraper, \"robots_handler\"), \"robots_handler not initialized\"\n68             assert hasattr(self.content_scraper, \"user_agent\"), \"user_agent not initialized\"\n", "col_offset": 12, "end_col_offset": 100, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 67, "line_range": [67], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "67             assert hasattr(self.content_scraper, \"robots_handler\"), \"robots_handler not initialized\"\n68             assert hasattr(self.content_scraper, \"user_agent\"), \"user_agent not initialized\"\n69 \n", "col_offset": 12, "end_col_offset": 92, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 68, "line_range": [68], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "96             # Check that robots.txt blocking works\n97             assert \"error\" in result, \"Should return error for blocked URL\"\n98             assert \"robots.txt\" in result[\"error\"].lower(), \"Error should mention robots.txt\"\n", "col_offset": 12, "end_col_offset": 75, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 97, "line_range": [97], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "97             assert \"error\" in result, \"Should return error for blocked URL\"\n98             assert \"robots.txt\" in result[\"error\"].lower(), \"Error should mention robots.txt\"\n99 \n", "col_offset": 12, "end_col_offset": 93, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 98, "line_range": [98], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "129 \n130             assert wait_called, \"Rate limiter wait_if_needed should be called\"\n131 \n", "col_offset": 12, "end_col_offset": 78, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 130, "line_range": [130], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "181 \n182             assert not missing_fields, f\"Missing required fields: {missing_fields}\"\n183 \n", "col_offset": 12, "end_col_offset": 83, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 182, "line_range": [182], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "184             # Check data types\n185             assert isinstance(result[\"heading_h1\"], list), \"heading_h1 should be a list\"\n186             assert isinstance(result[\"heading_h2\"], list), \"heading_h2 should be a list\"\n", "col_offset": 12, "end_col_offset": 88, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 185, "line_range": [185], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "185             assert isinstance(result[\"heading_h1\"], list), \"heading_h1 should be a list\"\n186             assert isinstance(result[\"heading_h2\"], list), \"heading_h2 should be a list\"\n187             assert isinstance(result[\"heading_h3\"], list), \"heading_h3 should be a list\"\n", "col_offset": 12, "end_col_offset": 88, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 186, "line_range": [186], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "186             assert isinstance(result[\"heading_h2\"], list), \"heading_h2 should be a list\"\n187             assert isinstance(result[\"heading_h3\"], list), \"heading_h3 should be a list\"\n188             assert isinstance(\n", "col_offset": 12, "end_col_offset": 88, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 187, "line_range": [187], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "187             assert isinstance(result[\"heading_h3\"], list), \"heading_h3 should be a list\"\n188             assert isinstance(\n189                 result[\"internal_links_count\"], int\n190             ), \"internal_links_count should be int\"\n191             assert isinstance(\n", "col_offset": 12, "end_col_offset": 51, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 188, "line_range": [188, 189, 190], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "190             ), \"internal_links_count should be int\"\n191             assert isinstance(\n192                 result[\"external_links_count\"], int\n193             ), \"external_links_count should be int\"\n194             assert isinstance(result[\"word_count\"], int), \"word_count should be int\"\n", "col_offset": 12, "end_col_offset": 51, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 191, "line_range": [191, 192, 193], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "193             ), \"external_links_count should be int\"\n194             assert isinstance(result[\"word_count\"], int), \"word_count should be int\"\n195 \n", "col_offset": 12, "end_col_offset": 84, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 194, "line_range": [194], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "214             # Should return error gracefully\n215             assert \"error\" in result, \"Should return error for invalid URL\"\n216             assert \"url\" in result, \"Should include original URL in error response\"\n", "col_offset": 12, "end_col_offset": 75, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 215, "line_range": [215], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "215             assert \"error\" in result, \"Should return error for invalid URL\"\n216             assert \"url\" in result, \"Should include original URL in error response\"\n217 \n", "col_offset": 12, "end_col_offset": 83, "filename": "./scripts/verify_milestone_1_5.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 216, "line_range": [216], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "63                     text(\n64                         f\"\"\"\n65                     SELECT COUNT(*) FROM information_schema.tables \n66                     WHERE table_schema = 'public' AND table_name = '{table}'\n67                 \"\"\"\n68                     )\n", "col_offset": 28, "end_col_offset": 68, "filename": "./scripts/verify_milestone_2_2.py", "issue_confidence": "LOW", "issue_cwe": {"id": 89, "link": "https://cwe.mitre.org/data/definitions/89.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible SQL injection vector through string-based query construction.", "line_number": 64, "line_range": [64, 65, 66, 67], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b608_hardcoded_sql_expressions.html", "test_id": "B608", "test_name": "hardcoded_sql_expressions"}, {"code": "10 import logging\n11 import subprocess\n12 from datetime import datetime\n", "col_offset": 0, "end_col_offset": 17, "filename": "./scripts/verify_milestone_2_3.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Consider possible security implications associated with the subprocess module.", "line_number": 11, "line_range": [11], "more_info": "https://bandit.readthedocs.io/en/1.8.5/blacklists/blacklist_imports.html#b404-import-subprocess", "test_id": "B404", "test_name": "blacklist"}, {"code": "141                 try:\n142                     result = subprocess.run(command, capture_output=True, text=True, timeout=10)\n143                     if result.returncode == 0:\n", "col_offset": 29, "end_col_offset": 96, "filename": "./scripts/verify_milestone_2_3.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 142, "line_range": [142], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "160             # Try to run pytest with dry-run to check if tests are discoverable\n161             result = subprocess.run(\n162                 [\"python\", \"-m\", \"pytest\", \"--collect-only\", \"-q\"],\n163                 cwd=self.project_root,\n164                 capture_output=True,\n165                 text=True,\n166                 timeout=30,\n167             )\n168 \n", "col_offset": 21, "end_col_offset": 13, "filename": "./scripts/verify_milestone_2_3.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 161, "line_range": [161, 162, 163, 164, 165, 166, 167], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "160             # Try to run pytest with dry-run to check if tests are discoverable\n161             result = subprocess.run(\n162                 [\"python\", \"-m\", \"pytest\", \"--collect-only\", \"-q\"],\n163                 cwd=self.project_root,\n164                 capture_output=True,\n165                 text=True,\n166                 timeout=30,\n167             )\n168 \n", "col_offset": 21, "end_col_offset": 13, "filename": "./scripts/verify_milestone_2_3.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 161, "line_range": [161, 162, 163, 164, 165, 166, 167], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "185             # Check if pytest-cov is available\n186             result = subprocess.run(\n187                 [\"python\", \"-c\", 'import pytest_cov; print(\"pytest-cov available\")'],\n188                 capture_output=True,\n189                 text=True,\n190                 timeout=10,\n191             )\n192 \n", "col_offset": 21, "end_col_offset": 13, "filename": "./scripts/verify_milestone_2_3.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "Starting a process with a partial executable path", "line_number": 186, "line_range": [186, 187, 188, 189, 190, 191], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b607_start_process_with_partial_path.html", "test_id": "B607", "test_name": "start_process_with_partial_path"}, {"code": "185             # Check if pytest-cov is available\n186             result = subprocess.run(\n187                 [\"python\", \"-c\", 'import pytest_cov; print(\"pytest-cov available\")'],\n188                 capture_output=True,\n189                 text=True,\n190                 timeout=10,\n191             )\n192 \n", "col_offset": 21, "end_col_offset": 13, "filename": "./scripts/verify_milestone_2_3.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 78, "link": "https://cwe.mitre.org/data/definitions/78.html"}, "issue_severity": "LOW", "issue_text": "subprocess call - check for execution of untrusted input.", "line_number": 186, "line_range": [186, 187, 188, 189, 190, 191], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b603_subprocess_without_shell_equals_true.html", "test_id": "B603", "test_name": "subprocess_without_shell_equals_true"}, {"code": "80 \n81         if config.password == \"secure_password_here\":\n82             print(\n", "col_offset": 30, "end_col_offset": 52, "filename": "./scripts/verify_setup.py", "issue_confidence": "MEDIUM", "issue_cwe": {"id": 259, "link": "https://cwe.mitre.org/data/definitions/259.html"}, "issue_severity": "LOW", "issue_text": "Possible hardcoded password: 'secure_password_here'", "line_number": 81, "line_range": [81], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b105_hardcoded_password_string.html", "test_id": "B105", "test_name": "hardcoded_password_string"}, {"code": "106             similarity_query = text(\n107                 f\"\"\"\n108                 WITH vector_similarities AS (\n109                     SELECT\n110                         pv.page_id,\n111                         p.url,\n112                         p.title,\n113                         p.meta_description,\n114                         -- Calculate cosine similarity manually\n115                         (\n116                             SELECT\n117                                 SUM(a.value::float * b.value::float) /\n118                                 (SQRT(SUM(a.value::float * a.value::float)) * SQRT(SUM(b.value::float * b.value::float)))\n119                             FROM jsonb_array_elements(pv.content_embedding) WITH ORDINALITY a(value, idx)\n120                             JOIN jsonb_array_elements('{query_vector_json}'::jsonb) WITH ORDINALITY b(value, idx2)\n121                                 ON a.idx = b.idx2\n122                         ) as similarity_score\n123                     FROM page_vectors pv\n124                     JOIN pages p ON pv.page_id = p.id\n125                     WHERE pv.content_embedding IS NOT NULL\n126                 )\n127                 SELECT page_id, url, title, meta_description, similarity_score\n128                 FROM vector_similarities\n129                 WHERE similarity_score > {similarity_threshold}\n130                 ORDER BY similarity_score DESC\n131                 LIMIT {limit}\n132             \"\"\"\n133             )\n", "col_offset": 20, "end_col_offset": 55, "filename": "./services/search/embedding_service.py", "issue_confidence": "LOW", "issue_cwe": {"id": 89, "link": "https://cwe.mitre.org/data/definitions/89.html"}, "issue_severity": "MEDIUM", "issue_text": "Possible SQL injection vector through string-based query construction.", "line_number": 107, "line_range": [107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b608_hardcoded_sql_expressions.html", "test_id": "B608", "test_name": "hardcoded_sql_expressions"}, {"code": "22 \n23         assert result[\"title\"] == \"Test Page\"\n24         assert \"<PERSON>\" in result[\"content_text\"]\n", "col_offset": 8, "end_col_offset": 45, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 23, "line_range": [23], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "23         assert result[\"title\"] == \"Test Page\"\n24         assert \"<PERSON>\" in result[\"content_text\"]\n25         assert result[\"http_status\"] == 200\n", "col_offset": 8, "end_col_offset": 53, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 24, "line_range": [24], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "24         assert \"<PERSON>\" in result[\"content_text\"]\n25         assert result[\"http_status\"] == 200\n26         assert len(result[\"links\"]) > 0\n", "col_offset": 8, "end_col_offset": 43, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 25, "line_range": [25], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "25         assert result[\"http_status\"] == 200\n26         assert len(result[\"links\"]) > 0\n27         # Ensure the link is correctly parsed\n", "col_offset": 8, "end_col_offset": 39, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 26, "line_range": [26], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "27         # Ensure the link is correctly parsed\n28         assert result[\"links\"][0][\"url\"] == \"https://example.com\"\n29         assert result[\"links\"][0][\"anchor_text\"] == \"External Link\"\n", "col_offset": 8, "end_col_offset": 65, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 28, "line_range": [28], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "28         assert result[\"links\"][0][\"url\"] == \"https://example.com\"\n29         assert result[\"links\"][0][\"anchor_text\"] == \"External Link\"\n30         assert not result[\"links\"][0][\"is_internal\"]  # example.com is external to test.com\n", "col_offset": 8, "end_col_offset": 67, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 29, "line_range": [29], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "29         assert result[\"links\"][0][\"anchor_text\"] == \"External Link\"\n30         assert not result[\"links\"][0][\"is_internal\"]  # example.com is external to test.com\n31 \n", "col_offset": 8, "end_col_offset": 52, "filename": "./tests/test_content_scraper.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 30, "line_range": [30], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "12     # Verify site was created\n13     assert site.id is not None\n14     assert site.domain == sample_site_data[\"domain\"]\n", "col_offset": 4, "end_col_offset": 30, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 13, "line_range": [13], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "13     assert site.id is not None\n14     assert site.domain == sample_site_data[\"domain\"]\n15     assert site.title == sample_site_data[\"title\"]\n", "col_offset": 4, "end_col_offset": 52, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 14, "line_range": [14], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "14     assert site.domain == sample_site_data[\"domain\"]\n15     assert site.title == sample_site_data[\"title\"]\n16     assert site.is_active is True\n", "col_offset": 4, "end_col_offset": 50, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 15, "line_range": [15], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "15     assert site.title == sample_site_data[\"title\"]\n16     assert site.is_active is True\n17 \n", "col_offset": 4, "end_col_offset": 33, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 16, "line_range": [16], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "31     # Verify URL frontier entry\n32     assert url_entry.id is not None\n33     assert url_entry.url == \"https://example.com/test\"\n", "col_offset": 4, "end_col_offset": 35, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 32, "line_range": [32], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "32     assert url_entry.id is not None\n33     assert url_entry.url == \"https://example.com/test\"\n34     assert url_entry.status == \"pending\"\n", "col_offset": 4, "end_col_offset": 54, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 33, "line_range": [33], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "33     assert url_entry.url == \"https://example.com/test\"\n34     assert url_entry.status == \"pending\"\n35     assert url_entry.site_id == site.id\n", "col_offset": 4, "end_col_offset": 40, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 34, "line_range": [34], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "34     assert url_entry.status == \"pending\"\n35     assert url_entry.site_id == site.id\n36 \n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 35, "line_range": [35], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "51     # Verify page was created\n52     assert page.id is not None\n53     assert page.url == sample_page_data[\"url\"]\n", "col_offset": 4, "end_col_offset": 30, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 52, "line_range": [52], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "52     assert page.id is not None\n53     assert page.url == sample_page_data[\"url\"]\n54     assert page.content_hash is not None\n", "col_offset": 4, "end_col_offset": 46, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 53, "line_range": [53], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "53     assert page.url == sample_page_data[\"url\"]\n54     assert page.content_hash is not None\n55     assert page.word_count > 0\n", "col_offset": 4, "end_col_offset": 40, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 54, "line_range": [54], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "54     assert page.content_hash is not None\n55     assert page.word_count > 0\n56     assert page.is_successful() is True\n", "col_offset": 4, "end_col_offset": 30, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 55, "line_range": [55], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "55     assert page.word_count > 0\n56     assert page.is_successful() is True\n57 \n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 56, "line_range": [56], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "80     # Verify link was created\n81     assert link.id is not None\n82     assert link.from_page_id == page.id\n", "col_offset": 4, "end_col_offset": 30, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 81, "line_range": [81], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "81     assert link.id is not None\n82     assert link.from_page_id == page.id\n83     assert link.to_url == \"https://example.com/linked-page\"\n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 82, "line_range": [82], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "82     assert link.from_page_id == page.id\n83     assert link.to_url == \"https://example.com/linked-page\"\n84     assert link.is_internal() is True\n", "col_offset": 4, "end_col_offset": 59, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 83, "line_range": [83], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "83     assert link.to_url == \"https://example.com/linked-page\"\n84     assert link.is_internal() is True\n85 \n", "col_offset": 4, "end_col_offset": 37, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 84, "line_range": [84], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "106     # Verify log entry\n107     assert log_entry.id is not None\n108     assert log_entry.status == \"success\"\n", "col_offset": 4, "end_col_offset": 35, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 107, "line_range": [107], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "107     assert log_entry.id is not None\n108     assert log_entry.status == \"success\"\n109     assert log_entry.http_status == 200\n", "col_offset": 4, "end_col_offset": 40, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 108, "line_range": [108], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "108     assert log_entry.status == \"success\"\n109     assert log_entry.http_status == 200\n110     assert log_entry.response_time_ms == 150\n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 109, "line_range": [109], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "109     assert log_entry.http_status == 200\n110     assert log_entry.response_time_ms == 150\n111 \n", "col_offset": 4, "end_col_offset": 44, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 110, "line_range": [110], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "130     # Test relationships\n131     assert page.site == site\n132     assert site.pages[0] == page\n", "col_offset": 4, "end_col_offset": 28, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 131, "line_range": [131], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "131     assert page.site == site\n132     assert site.pages[0] == page\n133     assert url_entry.site == site\n", "col_offset": 4, "end_col_offset": 32, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 132, "line_range": [132], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "132     assert site.pages[0] == page\n133     assert url_entry.site == site\n134     assert site.url_frontier[0] == url_entry\n", "col_offset": 4, "end_col_offset": 33, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 133, "line_range": [133], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "133     assert url_entry.site == site\n134     assert site.url_frontier[0] == url_entry\n", "col_offset": 4, "end_col_offset": 44, "filename": "./tests/test_database_setup.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 134, "line_range": [134], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "17 \n18         assert result[\"detected_language\"] in [\n19             \"en\",\n20             \"english\",\n21             \"LABEL_0\",\n22         ]  # model \"papluca/xlm-roberta-base-language-detection\" returns LABEL_0 for english\n23         assert result[\"confidence\"] > 0.8\n", "col_offset": 8, "end_col_offset": 9, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 18, "line_range": [18, 19, 20, 21, 22], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "22         ]  # model \"papluca/xlm-roberta-base-language-detection\" returns LABEL_0 for english\n23         assert result[\"confidence\"] > 0.8\n24 \n", "col_offset": 8, "end_col_offset": 41, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 23, "line_range": [23], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "33 \n34         assert \"ORG\" in result[\"entities\"]\n35         assert \"PERSON\" in result[\"entities\"]\n", "col_offset": 8, "end_col_offset": 42, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 34, "line_range": [34], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "34         assert \"ORG\" in result[\"entities\"]\n35         assert \"PERSON\" in result[\"entities\"]\n36         assert \"GPE\" in result[\"entities\"]\n", "col_offset": 8, "end_col_offset": 45, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 35, "line_range": [35], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "35         assert \"PERSON\" in result[\"entities\"]\n36         assert \"GPE\" in result[\"entities\"]\n37         assert \"Apple Inc.\" in result[\"entities\"][\"ORG\"]\n", "col_offset": 8, "end_col_offset": 42, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 36, "line_range": [36], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "36         assert \"GPE\" in result[\"entities\"]\n37         assert \"Apple Inc.\" in result[\"entities\"][\"ORG\"]\n38         assert \"Steve Jobs\" in result[\"entities\"][\"PERSON\"]\n", "col_offset": 8, "end_col_offset": 56, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 37, "line_range": [37], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "37         assert \"Apple Inc.\" in result[\"entities\"][\"ORG\"]\n38         assert \"Steve Jobs\" in result[\"entities\"][\"PERSON\"]\n39         assert \"Cupertino\" in result[\"entities\"][\"GPE\"]\n", "col_offset": 8, "end_col_offset": 59, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 38, "line_range": [38], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "38         assert \"Steve Jobs\" in result[\"entities\"][\"PERSON\"]\n39         assert \"Cupertino\" in result[\"entities\"][\"GPE\"]\n40         assert \"California\" in result[\"entities\"][\"GPE\"]\n", "col_offset": 8, "end_col_offset": 55, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 39, "line_range": [39], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "39         assert \"Cupertino\" in result[\"entities\"][\"GPE\"]\n40         assert \"California\" in result[\"entities\"][\"GPE\"]\n41 \n", "col_offset": 8, "end_col_offset": 56, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 40, "line_range": [40], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "55         # and the ContentAnalyzer converts this to a polarity score.\n56         assert pos_result[\"sentiment_polarity\"] > 0\n57         assert neg_result[\"sentiment_polarity\"] < 0\n", "col_offset": 8, "end_col_offset": 51, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 56, "line_range": [56], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "56         assert pos_result[\"sentiment_polarity\"] > 0\n57         assert neg_result[\"sentiment_polarity\"] < 0\n58         assert (\n", "col_offset": 8, "end_col_offset": 51, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 57, "line_range": [57], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "57         assert neg_result[\"sentiment_polarity\"] < 0\n58         assert (\n59             pos_result[\"sentiment_label\"].lower() == \"positive\"\n60         )  # or relevant positive label from model\n61         assert (\n", "col_offset": 8, "end_col_offset": 9, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 58, "line_range": [58, 59, 60], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "60         )  # or relevant positive label from model\n61         assert (\n62             neg_result[\"sentiment_label\"].lower() == \"negative\"\n63         )  # or relevant negative label from model\n", "col_offset": 8, "end_col_offset": 9, "filename": "./tests/test_nlp_analyzer.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 61, "line_range": [61, 62, 63], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "259 \n260     assert result[\"processing_successful\"] is True\n261     assert result[\"entities_found\"] == 7  # 5 unique entities, 2 re-mentions\n", "col_offset": 4, "end_col_offset": 50, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 260, "line_range": [260], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "260     assert result[\"processing_successful\"] is True\n261     assert result[\"entities_found\"] == 7  # 5 unique entities, 2 re-mentions\n262     assert result[\"relationships_found\"] > 0  # Expect co-occurrence relationships\n", "col_offset": 4, "end_col_offset": 40, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 261, "line_range": [261], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "261     assert result[\"entities_found\"] == 7  # 5 unique entities, 2 re-mentions\n262     assert result[\"relationships_found\"] > 0  # Expect co-occurrence relationships\n263 \n", "col_offset": 4, "end_col_offset": 44, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 262, "line_range": [262], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "270     # The second \"Apple\" mention updates the first \"Apple\" entity (from Apple Inc.).\n271     assert len(entity_add_calls) == 4\n272 \n", "col_offset": 4, "end_col_offset": 37, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 271, "line_range": [271], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "276     ]\n277     assert len(page_entity_add_calls) == 7  # One for each mention\n278 \n", "col_offset": 4, "end_col_offset": 42, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 277, "line_range": [277], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "287     # Sentence 3: (<PERSON>, Google) -> 1\n288     assert len(entity_relationship_add_calls) == 5\n289 \n", "col_offset": 4, "end_col_offset": 50, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 288, "line_range": [288], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "294     stored_names = [e[\"canonical_name\"] for e in result[\"entities\"]]\n295     assert \"Apple\" in stored_names  # Canonical name for \"Apple Inc.\" is \"Apple\"\n296     assert \"Steve Jobs\" in stored_names\n", "col_offset": 4, "end_col_offset": 34, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 295, "line_range": [295], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "295     assert \"Apple\" in stored_names  # Canonical name for \"Apple Inc.\" is \"Apple\"\n296     assert \"Steve Jobs\" in stored_names\n297     assert \"Google\" in stored_names  # Should be there even if \"updated\"\n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 296, "line_range": [296], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "296     assert \"Steve Jobs\" in stored_names\n297     assert \"Google\" in stored_names  # Should be there even if \"updated\"\n298 \n", "col_offset": 4, "end_col_offset": 35, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 297, "line_range": [297], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "300     rel_types = [r[\"type\"] for r in result[\"relationships\"]]\n301     assert all(t == \"co_occurrence\" for t in rel_types)\n302 \n", "col_offset": 4, "end_col_offset": 55, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 301, "line_range": [301], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "311     )\n312     assert apple_google_rel is not None\n313     assert apple_google_rel[\"context\"].startswith(\"Apple and Google are competitors.\")\n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 312, "line_range": [312], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "312     assert apple_google_rel is not None\n313     assert apple_google_rel[\"context\"].startswith(\"Apple and Google are competitors.\")\n314 \n", "col_offset": 4, "end_col_offset": 86, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 313, "line_range": [313], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "317     \"\"\"Test entity name normalization.\"\"\"\n318     assert entity_extractor._normalize_entity_name(\"  Apple Inc.  \", \"ORG\") == \"Apple\"  # Normalized\n319     assert entity_extractor._normalize_entity_name(\"apple inc\", \"ORG\") == \"Apple\"\n", "col_offset": 4, "end_col_offset": 86, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 318, "line_range": [318], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "318     assert entity_extractor._normalize_entity_name(\"  Apple Inc.  \", \"ORG\") == \"Apple\"  # Normalized\n319     assert entity_extractor._normalize_entity_name(\"apple inc\", \"ORG\") == \"Apple\"\n320     assert entity_extractor._normalize_entity_name(\"Apple Corp.\", \"ORG\") == \"Apple\"\n", "col_offset": 4, "end_col_offset": 81, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 319, "line_range": [319], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "319     assert entity_extractor._normalize_entity_name(\"apple inc\", \"ORG\") == \"Apple\"\n320     assert entity_extractor._normalize_entity_name(\"Apple Corp.\", \"ORG\") == \"Apple\"\n321     assert entity_extractor._normalize_entity_name(\"<PERSON><PERSON>\", \"PERSON\") == \"Steve Jobs\"\n", "col_offset": 4, "end_col_offset": 83, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 320, "line_range": [320], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "320     assert entity_extractor._normalize_entity_name(\"Apple Corp.\", \"ORG\") == \"Apple\"\n321     assert entity_extractor._normalize_entity_name(\"<PERSON>. <PERSON>\", \"PERSON\") == \"<PERSON>s\"\n322     assert entity_extractor._normalize_entity_name(\"  mr. bill gates \", \"PERSON\") == \"<PERSON> Gates\"\n", "col_offset": 4, "end_col_offset": 94, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 321, "line_range": [321], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "321     assert entity_extractor._normalize_entity_name(\"<PERSON>. <PERSON>\", \"PERSON\") == \"<PERSON> Jobs\"\n322     assert entity_extractor._normalize_entity_name(\"  mr. bill gates \", \"PERSON\") == \"Bill Gates\"\n323     assert (\n", "col_offset": 4, "end_col_offset": 97, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 322, "line_range": [322], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "322     assert entity_extractor._normalize_entity_name(\"  mr. bill gates \", \"PERSON\") == \"Bill Gates\"\n323     assert (\n324         entity_extractor._normalize_entity_name(\"New York City\", \"GPE\") == \"New York City\"\n325     )  # No change for GPE\n326     assert (\n", "col_offset": 4, "end_col_offset": 5, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 323, "line_range": [323, 324, 325], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "325     )  # No change for GPE\n326     assert (\n327         entity_extractor._normalize_entity_name(\"The Great Gatsby\", \"WORK_OF_ART\")\n328         == \"The Great Gatsby\"\n329     )\n330 \n", "col_offset": 4, "end_col_offset": 5, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 326, "line_range": [326, 327, 328, 329], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "335     result_empty = await entity_extractor.extract_entities(SAMPLE_EMPTY_PAGE_DATA)\n336     assert result_empty[\"error\"] == \"Insufficient content for entity extraction\"\n337     assert result_empty.get(\"processing_successful\") is False\n", "col_offset": 4, "end_col_offset": 80, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 336, "line_range": [336], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "336     assert result_empty[\"error\"] == \"Insufficient content for entity extraction\"\n337     assert result_empty.get(\"processing_successful\") is False\n338 \n", "col_offset": 4, "end_col_offset": 61, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 337, "line_range": [337], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "339     result_short = await entity_extractor.extract_entities(SAMPLE_SHORT_PAGE_DATA)\n340     assert result_short[\"error\"] == \"Insufficient content for entity extraction\"\n341     assert result_short.get(\"processing_successful\") is False\n", "col_offset": 4, "end_col_offset": 80, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 340, "line_range": [340], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "340     assert result_short[\"error\"] == \"Insufficient content for entity extraction\"\n341     assert result_short.get(\"processing_successful\") is False\n342 \n", "col_offset": 4, "end_col_offset": 61, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 341, "line_range": [341], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "354 \n355     assert result[\"processing_successful\"] is False\n356     assert result[\"error\"] == \"spaCy processing error\"\n", "col_offset": 4, "end_col_offset": 51, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 355, "line_range": [355], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "355     assert result[\"processing_successful\"] is False\n356     assert result[\"error\"] == \"spaCy processing error\"\n357     # Can't directly assert logging.error calls without further mocking framework like caplog\n", "col_offset": 4, "end_col_offset": 54, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 356, "line_range": [356], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "385 \n386     assert result[\"processing_successful\"] is True\n387     assert result[\"entities_found\"] == 0\n", "col_offset": 4, "end_col_offset": 50, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 386, "line_range": [386], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "386     assert result[\"processing_successful\"] is True\n387     assert result[\"entities_found\"] == 0\n388     assert result[\"relationships_found\"] == 0\n", "col_offset": 4, "end_col_offset": 40, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 387, "line_range": [387], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "387     assert result[\"entities_found\"] == 0\n388     assert result[\"relationships_found\"] == 0\n389     assert len(result[\"entities\"]) == 0\n", "col_offset": 4, "end_col_offset": 45, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 388, "line_range": [388], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "388     assert result[\"relationships_found\"] == 0\n389     assert len(result[\"entities\"]) == 0\n390     assert len(result[\"relationships\"]) == 0\n", "col_offset": 4, "end_col_offset": 39, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 389, "line_range": [389], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "389     assert len(result[\"entities\"]) == 0\n390     assert len(result[\"relationships\"]) == 0\n391     # In _store_entities and _store_relationships, commit is only called if there's something to process.\n", "col_offset": 4, "end_col_offset": 44, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 390, "line_range": [390], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "417     entities = entity_extractor._extract_entities_from_doc(doc)\n418     assert len(entities) == 1\n419     entity_data = entities[0]\n", "col_offset": 4, "end_col_offset": 29, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 418, "line_range": [418], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "420 \n421     assert len(entity_data[\"context_before\"]) <= 100\n422     assert len(entity_data[\"context_after\"]) <= 100\n", "col_offset": 4, "end_col_offset": 52, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 421, "line_range": [421], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "421     assert len(entity_data[\"context_before\"]) <= 100\n422     assert len(entity_data[\"context_after\"]) <= 100\n423     assert entity_data[\"context_before\"].endswith(\n", "col_offset": 4, "end_col_offset": 51, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 422, "line_range": [422], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "422     assert len(entity_data[\"context_after\"]) <= 100\n423     assert entity_data[\"context_before\"].endswith(\n424         \"This is a very long text.\"\n425     )  # Removed trailing space\n426     assert entity_data[\"context_after\"].startswith(\n", "col_offset": 4, "end_col_offset": 5, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 423, "line_range": [423, 424, 425], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "425     )  # Removed trailing space\n426     assert entity_data[\"context_after\"].startswith(\n427         \"And the text continues for a while.\"\n428     )  # Removed leading space\n429 \n", "col_offset": 4, "end_col_offset": 5, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 426, "line_range": [426, 427, 428], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "449     relationships = entity_extractor._find_entity_relationships(mock_doc, extracted_entities)\n450     assert len(relationships) == 0\n451 \n", "col_offset": 4, "end_col_offset": 34, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 450, "line_range": [450], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "484 \n485     assert mock_existing_entity.frequency_count == 6\n486     assert mock_existing_entity.last_mentioned > datetime(2023, 1, 1)\n", "col_offset": 4, "end_col_offset": 52, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 485, "line_range": [485], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "485     assert mock_existing_entity.frequency_count == 6\n486     assert mock_existing_entity.last_mentioned > datetime(2023, 1, 1)\n487 \n", "col_offset": 4, "end_col_offset": 69, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 486, "line_range": [486], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "491     )\n492     assert page_entity_call[0][0].entity_id == existing_entity_id\n493     assert page_entity_call[0][0].page_id == page_id\n", "col_offset": 4, "end_col_offset": 65, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 492, "line_range": [492], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "492     assert page_entity_call[0][0].entity_id == existing_entity_id\n493     assert page_entity_call[0][0].page_id == page_id\n494     assert page_entity_call[0][0].mention_text == \"TestEntity Corp\"\n", "col_offset": 4, "end_col_offset": 52, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 493, "line_range": [493], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "493     assert page_entity_call[0][0].page_id == page_id\n494     assert page_entity_call[0][0].mention_text == \"TestEntity Corp\"\n495 \n", "col_offset": 4, "end_col_offset": 67, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 494, "line_range": [494], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "555 \n556     assert mock_existing_rel.co_occurrence_count == 4\n557     assert mock_existing_rel.last_seen > datetime(2023, 1, 1)\n", "col_offset": 4, "end_col_offset": 53, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 556, "line_range": [556], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "556     assert mock_existing_rel.co_occurrence_count == 4\n557     assert mock_existing_rel.last_seen > datetime(2023, 1, 1)\n558     assert \"new context\" in mock_existing_rel.example_contexts\n", "col_offset": 4, "end_col_offset": 61, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 557, "line_range": [557], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "557     assert mock_existing_rel.last_seen > datetime(2023, 1, 1)\n558     assert \"new context\" in mock_existing_rel.example_contexts\n559     assert len(mock_existing_rel.example_contexts) == 2  # old + new\n", "col_offset": 4, "end_col_offset": 62, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 558, "line_range": [558], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "558     assert \"new context\" in mock_existing_rel.example_contexts\n559     assert len(mock_existing_rel.example_contexts) == 2  # old + new\n560 \n", "col_offset": 4, "end_col_offset": 55, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 559, "line_range": [559], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}, {"code": "567     ]\n568     assert len(entity_rel_add_calls) == 0\n569 \n", "col_offset": 4, "end_col_offset": 41, "filename": "./tests/unit/nlp/test_entity_extractor.py", "issue_confidence": "HIGH", "issue_cwe": {"id": 703, "link": "https://cwe.mitre.org/data/definitions/703.html"}, "issue_severity": "LOW", "issue_text": "Use of assert detected. The enclosed code will be removed when compiling to optimised byte code.", "line_number": 568, "line_range": [568], "more_info": "https://bandit.readthedocs.io/en/1.8.5/plugins/b101_assert_used.html", "test_id": "B101", "test_name": "assert_used"}]}