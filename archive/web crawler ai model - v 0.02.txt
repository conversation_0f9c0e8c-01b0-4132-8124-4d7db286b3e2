import os
from bs4 import BeautifulSoup
import requests
import json

dir_path = '/path/to/directory'

# Create an empty list to store the URLs

urls = []

# Loop through each file in the directory and add each line of the file to the list of URLs

for file in os.listdir(dir_path):
if file.endswith(".txt"):
with open(os.path.join(dir_path, file), 'r') as f:
urls.extend(f.readlines())

# Remove any leading or trailing whitespace from the URLs in the list

urls = [url.strip() for url in urls]

# Create an empty list to store the scraped data

data = []

# Loop through each URL in the list and make a GET request to fetch the raw HTML content

for url in urls:
response = requests.get(url)
html = response.text


# Parse the HTML content using BeautifulSoup and scrape the desired data

soup = BeautifulSoup(html, 'html.parser')
title = soup.find('title').text
body = soup.find('body').text

# Store the scraped data in a dictionary and add the dictionary to the list of scraped data

data.append({'title': title, 'body': body})
Write the list of scraped data to a JSON file
with open('data.json', 'w') as f:
json.dump(data, f)

# Loop through each file in the directory again and delete the text files and the JSON data file

for file in os.listdir(dir_path):
if file.endswith(".txt") or file.endswith(".json"):
os.remove(os.path.join(dir_path, file))
