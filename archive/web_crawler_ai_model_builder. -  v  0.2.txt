import os
import requests
import json
from bs4 import BeautifulSoup

# Set the directory path where the text files are located

dir_path = '/path/to/directory'

# Create an empty list to store the URLs

urls = []

# Loop through each file in the directory

for file in os.listdir(dir_path):
# Check if the file is a text file

if file.endswith(".txt"):
# Read the contents of the text file

with open(os.path.join(dir_path, file), 'r') as f:

# Add each line of the file to the list of URLs

urls.extend(f.readlines())

# Remove any leading or trailing whitespace from the URLs

urls = [url.strip() for url in urls]

# Create an empty list to store the scraped data

data = []

# Loop through each URL

for url in urls:

# Make a GET request to fetch the raw HTML content

html_content = requests.get(url).text

# Parse the html content

soup = BeautifulSoup(html_content, "lxml")

# Scrape the desired data

title = soup.find("title").text
body = soup.find("body").text

# Store the scraped data in a dictionary

scraped_data = {
    'title': title,
    'body': body
}

# Add the dictionary to the list

data.append(scraped_data)

# Write the list of scraped data to a JSON file

with open('data.json', 'w') as f:
json.dump(data, f)

# Loop through each file in the directory again
for file in os.listdir(dir_path):
    # Check if the file is a text file
    if file.endswith(".txt"):
        # Delete the text file
        os.remove(os.path.join(dir_path, file))



