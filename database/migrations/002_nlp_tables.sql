-- Milestone 2.1: NLP Content Analysis Pipeline
-- Database schema for NLP processing results

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- NLP processing results
CREATE TABLE IF NOT EXISTS nlp_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,

    -- Language detection
    detected_language VARCHAR(10),
    language_confidence FLOAT,

    -- Topic classification
    primary_topic VARCHAR(100),
    topic_confidence FLOAT,
    topic_hierarchy JSONB, -- Hierarchical topic classification

    -- Named entity recognition
    entities JSONB, -- {type: [entities]} format
    entity_count INTEGER,

    -- Keyword extraction
    keywords JSONB, -- [{keyword, score, frequency}]
    key_phrases JSONB,

    -- Sentiment analysis
    sentiment_polarity FLOAT, -- -1 to 1
    sentiment_subjectivity FLOAT, -- 0 to 1
    emotion_scores JSONB, -- joy, anger, fear, etc.

    -- Content quality metrics
    readability_score FLOAT,
    grammar_score FLOAT,
    coherence_score FLOAT,

    -- Content structure analysis
    paragraph_count INTEGER,
    sentence_count INTEGER,
    avg_sentence_length FLOAT,
    complexity_score FLOAT,

    -- Processing metadata
    model_version VARCHAR(50),
    processing_time_ms INTEGER,
    processed_at TIMESTAMP DEFAULT NOW(),

    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for nlp_analysis
CREATE INDEX IF NOT EXISTS idx_nlp_analysis_page_id ON nlp_analysis(page_id);
CREATE INDEX IF NOT EXISTS idx_nlp_analysis_primary_topic ON nlp_analysis(primary_topic);
CREATE INDEX IF NOT EXISTS idx_nlp_analysis_language ON nlp_analysis(detected_language);
CREATE INDEX IF NOT EXISTS idx_nlp_analysis_sentiment ON nlp_analysis(sentiment_polarity);

-- Topic taxonomy for hierarchical classification
CREATE TABLE IF NOT EXISTS topic_taxonomy (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic_name VARCHAR(100) UNIQUE NOT NULL,
    parent_topic_id UUID REFERENCES topic_taxonomy(id),
    level INTEGER NOT NULL, -- 0=root, 1=category, 2=subcategory, etc.
    description TEXT,
    keywords TEXT[],

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for topic_taxonomy
CREATE INDEX IF NOT EXISTS idx_topic_taxonomy_parent ON topic_taxonomy(parent_topic_id);
CREATE INDEX IF NOT EXISTS idx_topic_taxonomy_level ON topic_taxonomy(level);

-- Insert initial topic taxonomy data
INSERT INTO topic_taxonomy (topic_name, parent_topic_id, level, description, keywords) VALUES
    ('Technology', NULL, 0, 'Technology and computing related content', ARRAY['tech', 'computer', 'software', 'hardware', 'programming']),
    ('Business', NULL, 0, 'Business and finance related content', ARRAY['business', 'finance', 'economy', 'market', 'company']),
    ('Science', NULL, 0, 'Scientific and research content', ARRAY['science', 'research', 'study', 'experiment', 'discovery']),
    ('Health', NULL, 0, 'Health and medical content', ARRAY['health', 'medical', 'medicine', 'wellness', 'fitness']),
    ('Politics', NULL, 0, 'Political and government content', ARRAY['politics', 'government', 'policy', 'election', 'law']),
    ('Entertainment', NULL, 0, 'Entertainment and media content', ARRAY['entertainment', 'movie', 'music', 'celebrity', 'show']),
    ('Sports', NULL, 0, 'Sports and athletics content', ARRAY['sports', 'game', 'team', 'player', 'competition']),
    ('Education', NULL, 0, 'Educational and academic content', ARRAY['education', 'school', 'university', 'learning', 'academic']),
    ('Travel', NULL, 0, 'Travel and tourism content', ARRAY['travel', 'tourism', 'destination', 'vacation', 'trip']),
    ('Food', NULL, 0, 'Food and cooking content', ARRAY['food', 'recipe', 'cooking', 'restaurant', 'cuisine'])
ON CONFLICT (topic_name) DO NOTHING;
