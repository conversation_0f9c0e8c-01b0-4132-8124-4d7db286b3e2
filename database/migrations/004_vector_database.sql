-- Vector embeddings storage (fallback implementation without pgvector)
-- Note: This uses JSONB for vector storage until pgvector extension is available

CREATE TABLE page_vectors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    page_id UUID REFERENCES pages(id) ON DELETE CASCADE,

    -- Vector embeddings stored as JSONB arrays (fallback)
    content_embedding JSONB, -- Full content embedding as array
    title_embedding JSONB,   -- Title-specific embedding as array
    summary_embedding JSONB, -- Summary embedding as array

    -- Embedding metadata
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50) NOT NULL,
    embedding_created_at TIMESTAMP DEFAULT NOW(),

    -- Content metadata for search optimization
    content_length INTEGER,
    language VARCHAR(10),
    content_hash VARCHAR(64),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for search optimization
CREATE INDEX idx_page_vectors_page_id ON page_vectors(page_id);
CREATE INDEX idx_page_vectors_language ON page_vectors(language);
CREATE INDEX idx_page_vectors_model ON page_vectors(model_name, model_version);
CREATE INDEX idx_page_vectors_content_hash ON page_vectors(content_hash);

-- Search query history for analytics
CREATE TABLE search_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    query_embedding JSONB, -- Query embedding as JSONB array (fallback)

    -- Search parameters
    search_type VARCHAR(20), -- semantic, hybrid, keyword
    limit_requested INTEGER,
    filters_applied JSONB,

    -- Results metadata
    results_count INTEGER,
    response_time_ms INTEGER,

    -- User context (if applicable)
    user_id VARCHAR(100),
    session_id VARCHAR(100),
    ip_address INET,
    user_agent TEXT,

    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX idx_search_queries_query_text ON search_queries(query_text);
CREATE INDEX idx_search_queries_user_id ON search_queries(user_id);
