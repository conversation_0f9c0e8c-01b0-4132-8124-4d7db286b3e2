-- 01_initial_setup.sql
-- Initial database setup for Universal Web Directory

-- Database creation and user setup (run as postgres superuser)
-- CREATE DATABASE web_crawler;
-- CREATE USER webc WITH PASSWORD 'j9xuvUyTHBKEld4JeP9FO';
-- GRANT ALL PRIVILEGES ON DATABASE web_crawler TO webc;

-- Connect to the web_crawler database before running the rest

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";  -- For fuzzy text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- For composite indexes
-- CREATE EXTENSION IF NOT EXISTS "pgvector";  -- For vector embeddings (install separately)

-- Create trigger function to update updated_at timestamp
-- This function can be shared by multiple tables if needed.
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Note: Tables, indexes, and specific triggers (like attaching update_updated_at_column to sites)
-- are now expected to be handled by SQLAlchemy models and migrations.
-- Granting permissions might still be relevant here if not handled elsewhere.

-- Grant permissions to application user (if not handled by SQLAlchemy/migrations or initial user setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO webc;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO webc;
