-- Milestone 1.1: Advanced URL Frontier System Migration
-- This migration enhances the URL frontier with enterprise-grade features

-- First, backup existing data
CREATE TEMP TABLE url_frontier_backup AS SELECT * FROM url_frontier;

-- Drop existing table and recreate with advanced schema
DROP TABLE IF EXISTS url_frontier CASCADE;

-- Advanced URL Frontier Table (Milestone 1.1)
CREATE TABLE url_frontier (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT UNIQUE NOT NULL,
    url_hash VARCHAR(64) UNIQUE NOT NULL, -- SHA-256 for deduplication
    domain VARCHAR(255) NOT NULL,
    subdomain VARCHAR(255),
    path TEXT,
    query_params JSONB,

    -- Crawling metadata
    status VARCHAR(20) DEFAULT 'pending', -- pending, crawling, crawled, error, blocked
    priority INTEGER DEFAULT 5, -- 1-10 scale, 10 = highest
    source_type VARCHAR(50), -- seed, discovered, sitemap, robots
    discovered_at TIMESTAMP DEFAULT NOW(),
    scheduled_for TIMESTAMP DEFAULT NOW(),
    last_crawled TIMESTAMP,
    next_crawl TIMESTAMP,
    crawl_attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,

    -- Error handling
    error_message TEXT,
    error_type VARCHAR(50),
    http_status INTEGER,

    -- Metadata
    content_type VARCHAR(100),
    content_length BIGINT,
    last_modified TIMESTAMP,
    etag VARCHAR(255),

    -- Worker coordination
    worker_id VARCHAR(100),

    -- Indexing and performance
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_url_frontier_status ON url_frontier(status);
CREATE INDEX idx_url_frontier_priority ON url_frontier(priority DESC);
CREATE INDEX idx_url_frontier_domain ON url_frontier(domain);
CREATE INDEX idx_url_frontier_scheduled ON url_frontier(scheduled_for) WHERE status = 'pending';
CREATE INDEX idx_url_frontier_next_crawl ON url_frontier(next_crawl) WHERE status = 'crawled';
CREATE UNIQUE INDEX idx_url_frontier_hash ON url_frontier(url_hash);
CREATE INDEX idx_url_frontier_worker ON url_frontier(worker_id) WHERE worker_id IS NOT NULL;

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_url_frontier_updated_at BEFORE UPDATE
    ON url_frontier FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Migrate existing data with URL parsing and hashing
INSERT INTO url_frontier (
    id, url, url_hash, domain, subdomain, path, 
    status, priority, source_type, discovered_at, 
    scheduled_for, crawl_attempts, max_attempts, error_message
)
SELECT 
    id,
    url,
    encode(sha256(url::bytea), 'hex') as url_hash,
    CASE 
        WHEN url ~ '^https?://' THEN 
            split_part(split_part(url, '://', 2), '/', 1)
        ELSE 'unknown'
    END as domain,
    CASE 
        WHEN url ~ '^https?://' THEN 
            split_part(split_part(url, '://', 2), '/', 1)
        ELSE NULL
    END as subdomain,
    CASE 
        WHEN url ~ '^https?://' THEN 
            '/' || array_to_string(
                (string_to_array(split_part(url, '://', 2), '/'))[2:], 
                '/'
            )
        ELSE NULL
    END as path,
    status,
    priority,
    COALESCE(source_type, 'migrated') as source_type,
    discovered_at,
    scheduled_for,
    attempts as crawl_attempts,
    max_attempts,
    error_message
FROM url_frontier_backup
ON CONFLICT (url_hash) DO NOTHING;

-- Drop temporary backup table
DROP TABLE url_frontier_backup;

-- Add comments for documentation
COMMENT ON TABLE url_frontier IS 'Advanced URL frontier system for enterprise-grade web crawling';
COMMENT ON COLUMN url_frontier.url_hash IS 'SHA-256 hash of URL for fast deduplication';
COMMENT ON COLUMN url_frontier.status IS 'Current crawling status: pending, crawling, crawled, error, blocked';
COMMENT ON COLUMN url_frontier.priority IS 'Crawling priority (1-10, higher = more important)';
COMMENT ON COLUMN url_frontier.worker_id IS 'ID of worker currently processing this URL';
