-- Missing indexes for Advanced URL Frontier System
-- These indexes were not created in the initial migration

-- Index for scheduled_for column (used for priority scheduling)
CREATE INDEX IF NOT EXISTS idx_url_frontier_scheduled_for 
ON url_frontier(scheduled_for);

-- Composite index for worker coordination (status + worker_id)
CREATE INDEX IF NOT EXISTS idx_url_frontier_worker_coordination 
ON url_frontier(status, worker_id) 
WHERE status IN ('pending', 'crawling');

-- Index for URL hash (used for fast deduplication)
CREATE INDEX IF NOT EXISTS idx_url_frontier_url_hash 
ON url_frontier(url_hash);

-- Additional performance indexes
CREATE INDEX IF NOT EXISTS idx_url_frontier_next_crawl_pending 
ON url_frontier(next_crawl) 
WHERE status = 'pending' AND next_crawl IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_url_frontier_error_type 
ON url_frontier(error_type) 
WHERE error_type IS NOT NULL;

-- Analyze table for query optimization
ANALYZE url_frontier;

-- Display index information
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'url_frontier'
ORDER BY indexname;
