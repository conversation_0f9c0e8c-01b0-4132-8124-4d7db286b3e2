-- Rate limiting tracking
CREATE TABLE rate_limit_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL,
    worker_id VARCHAR(100),

    -- Request tracking
    requests_last_minute INTEGER DEFAULT 0,
    requests_last_hour INTEGER DEFAULT 0,
    requests_last_day INTEGER DEFAULT 0,

    -- Timing
    last_request_at TIMESTAMP,
    next_allowed_request TIMESTAMP DEFAULT NOW(),

    -- Adaptive rate limiting
    current_delay_ms INTEGER DEFAULT 1000,
    success_rate FLOAT DEFAULT 1.0,
    consecutive_failures INTEGER DEFAULT 0,

    -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_rate_limit_domain_worker ON rate_limit_tracking(domain, worker_id);
CREATE INDEX idx_rate_limit_next_allowed ON rate_limit_tracking(next_allowed_request);

-- Request history for analytics
CREATE TABLE request_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) NOT NULL,
    url TEXT NOT NULL,
    worker_id VARCHAR(100),

    -- Request details
    method VARCHAR(10) DEFAULT 'GET',
    status_code INTEGER,
    response_time_ms INTEGER,
    content_length BIGINT,

    -- Success/failure tracking
    success BOOLEAN,
    error_type VARCHAR(50),
    error_message TEXT,

    -- Timing
    requested_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

CREATE INDEX idx_request_history_domain ON request_history(domain);
CREATE INDEX idx_request_history_requested_at ON request_history(requested_at);
CREATE INDEX idx_request_history_success ON request_history(success);

-- Trigger for updated_at on rate_limit_tracking
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_rate_limit_tracking_updated_at BEFORE UPDATE
    ON rate_limit_tracking FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
