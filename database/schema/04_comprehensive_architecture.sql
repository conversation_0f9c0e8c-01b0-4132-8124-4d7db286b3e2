-- Milestone 1.4: Comprehensive Database Architecture Migration
-- This script migrates the existing simple schema to the comprehensive Universal Web Directory schema

-- First, backup existing data by creating temporary tables
CREATE TABLE sites_backup AS SELECT * FROM sites;
CREATE TABLE pages_backup AS SELECT * FROM pages;

-- Drop existing tables (this will cascade to dependent tables)
DROP TABLE IF EXISTS pages CASCADE;
DROP TABLE IF EXISTS sites CASCADE;

-- Create comprehensive sites table
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,
    subdomain VARCHAR(255),

    -- Site metadata
    title TEXT,
    description TEXT,
    language VARCHAR(10),
    country VARCHAR(3),

    -- Technical information
    ip_address INET,
    server_software VARCHAR(100),
    cms_platform VARCHAR(100),
    technology_stack JSONB,

    -- SEO and structure
    robots_txt_url TEXT,
    sitemap_urls TEXT[],
    favicon_url TEXT,

    -- Performance metrics
    avg_response_time INTEGER, -- milliseconds
    uptime_percentage FLOAT,
    ssl_enabled BOOLEAN DEFAULT FALSE,
    mobile_friendly BOOLEAN,

    -- Content characteristics
    estimated_page_count INTEGER,
    content_freshness_score FLOAT,
    update_frequency VARCHAR(20), -- daily, weekly, monthly, etc.

    -- Authority and trust
    domain_age_days INTEGER,
    backlink_count INTEGER,
    authority_score FLOAT,
    spam_score FLOAT,

    -- Crawling metadata
    first_discovered TIMESTAMP DEFAULT NOW(),
    last_crawled TIMESTAMP,
    next_crawl_scheduled TIMESTAMP,
    crawl_frequency_hours INTEGER DEFAULT 168, -- Weekly default
    crawl_priority INTEGER DEFAULT 5,

    -- Status tracking
    status VARCHAR(20) DEFAULT 'active', -- active, blocked, error, suspended
    error_count INTEGER DEFAULT 0,
    last_error TEXT,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create comprehensive pages table
CREATE TABLE pages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    url TEXT UNIQUE NOT NULL,
    url_hash VARCHAR(64) UNIQUE NOT NULL,

    -- Page metadata
    title TEXT,
    meta_description TEXT,
    meta_keywords TEXT[],
    canonical_url TEXT,

    -- Content
    content_text TEXT,
    content_html TEXT,
    content_length INTEGER,
    word_count INTEGER,

    -- Structure
    heading_h1 TEXT[],
    heading_h2 TEXT[],
    heading_h3 TEXT[],
    internal_links_count INTEGER,
    external_links_count INTEGER,
    image_count INTEGER,

    -- Technical details
    http_status INTEGER,
    content_type VARCHAR(100),
    charset VARCHAR(50),
    response_time_ms INTEGER,

    -- SEO metrics
    page_rank_score FLOAT,
    readability_score FLOAT,
    keyword_density JSONB,

    -- Social signals
    social_shares_count INTEGER,
    comments_count INTEGER,

    -- Content classification
    content_type_classification VARCHAR(50), -- article, product, homepage, etc.
    topic_categories TEXT[],
    sentiment_score FLOAT,

    -- Crawling metadata
    first_discovered TIMESTAMP DEFAULT NOW(),
    last_crawled TIMESTAMP DEFAULT NOW(),
    last_modified TIMESTAMP,
    etag VARCHAR(255),

    -- Change tracking
    content_hash VARCHAR(64),
    previous_content_hash VARCHAR(64),
    change_frequency VARCHAR(20),

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for sites table
CREATE INDEX idx_sites_domain ON sites(domain);
CREATE INDEX idx_sites_last_crawled ON sites(last_crawled);
CREATE INDEX idx_sites_next_crawl ON sites(next_crawl_scheduled);
CREATE INDEX idx_sites_status ON sites(status);
CREATE INDEX idx_sites_authority_score ON sites(authority_score);

-- Create indexes for pages table
CREATE INDEX idx_pages_site_id ON pages(site_id);
CREATE INDEX idx_pages_url_hash ON pages(url_hash);
CREATE INDEX idx_pages_last_crawled ON pages(last_crawled);
CREATE INDEX idx_pages_content_type ON pages(content_type_classification);
CREATE INDEX idx_pages_topic_categories ON pages USING GIN(topic_categories);
CREATE INDEX idx_pages_http_status ON pages(http_status);

-- Create triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_pages_updated_at BEFORE UPDATE ON pages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Migrate existing data from backup tables
-- Migrate sites data
INSERT INTO sites (
    id, domain, title, description, language,
    last_crawled, created_at, updated_at,
    status, robots_txt_url
)
SELECT
    id, domain, title, description,
    COALESCE(language, 'en'),
    last_crawled, created_at, updated_at,
    CASE WHEN is_active THEN 'active' ELSE 'suspended' END,
    CASE WHEN robots_txt IS NOT NULL THEN 'http://' || domain || '/robots.txt' END
FROM sites_backup;

-- Migrate pages data with URL hash generation
INSERT INTO pages (
    id, site_id, url, url_hash, title,
    content_text, content_html, content_length, word_count,
    http_status, content_type, last_crawled, last_modified, etag,
    content_hash, created_at, updated_at
)
SELECT
    id, site_id, url,
    encode(sha256(url::bytea), 'hex'),
    title,
    text_content, html_content, content_length, word_count,
    http_status, content_type, crawled_at, last_modified, etag,
    content_hash, crawled_at, crawled_at
FROM pages_backup;

-- Clean up backup tables
DROP TABLE sites_backup;
DROP TABLE pages_backup;

-- Add comments for documentation
COMMENT ON TABLE sites IS 'Domain-level information for Universal Web Directory';
COMMENT ON TABLE pages IS 'Individual page information with comprehensive metadata';
COMMENT ON COLUMN sites.authority_score IS 'Domain authority score (0.0-1.0)';
COMMENT ON COLUMN sites.spam_score IS 'Spam likelihood score (0.0-1.0)';
COMMENT ON COLUMN pages.sentiment_score IS 'Content sentiment analysis (-1.0 to 1.0)';
COMMENT ON COLUMN pages.keyword_density IS 'JSONB object with keyword frequency analysis';
