apiVersion: v1
kind: ConfigMap
metadata:
  name: web-crawler-config
  labels:
    app: web-crawler-api
data:
  # Application configuration
  PYTHONPATH: "/app"
  LOG_LEVEL: "INFO"
  
  # API configuration
  API_HOST: "0.0.0.0"
  API_PORT: "8000"
  API_WORKERS: "4"
  
  # Crawler configuration
  MAX_CONCURRENT_REQUESTS: "10"
  REQUEST_DELAY: "1.0"
  USER_AGENT: "Universal-Web-Directory/1.0"
  
  # Rate limiting
  RATE_LIMIT_REQUESTS: "100"
  RATE_LIMIT_WINDOW: "60"
  
  # Monitoring
  METRICS_ENABLED: "true"
  HEALTH_CHECK_INTERVAL: "30"
