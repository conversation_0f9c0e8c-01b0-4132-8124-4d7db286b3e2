apiVersion: apps/v1
kind: Deployment
metadata:
  name: web-crawler-api
  labels:
    app: web-crawler-api
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: web-crawler-api
  template:
    metadata:
      labels:
        app: web-crawler-api
        version: v1
    spec:
      containers:
      - name: api
        image: web-crawler:latest
        ports:
        - containerPort: 8000
          name: http
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: web-crawler-secrets
              key: database-url
        - name: PYTHONPATH
          value: "/app"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
      volumes:
      - name: data-volume
        persistentVolumeClaim:
          claimName: web-crawler-data-pvc
      restartPolicy: Always
