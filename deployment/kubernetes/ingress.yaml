apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: web-crawler-ingress
  labels:
    app: web-crawler-api
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - api.webcrawler.example.com
    secretName: web-crawler-tls
  rules:
  - host: api.webcrawler.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: web-crawler-api-service
            port:
              number: 80
