apiVersion: v1
kind: Secret
metadata:
  name: web-crawler-secrets
  labels:
    app: web-crawler-api
type: Opaque
data:
  # Base64 encoded values - replace with actual encoded secrets
  # To encode: echo -n "your-secret" | base64
  
  # Database URL (example - replace with actual encoded value)
  # *****************************************************/web_crawler
  database-url: cG9zdGdyZXNxbDovL3dlYmM6ajl4dXZVeVRIQktFbGQ0SmVQOUZPQHBvc3RncmVzOjU0MzIvd2ViX2NyYXdsZXI=
  
  # JWT secret key (example - replace with actual encoded value)
  jwt-secret: bXktc2VjcmV0LWp3dC1rZXktZm9yLXdlYi1jcmF3bGVy
  
  # API keys for external services (add as needed)
  # openai-api-key: ""
  # anthropic-api-key: ""
