apiVersion: v1
kind: Service
metadata:
  name: web-crawler-api-service
  labels:
    app: web-crawler-api
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: web-crawler-api

---
apiVersion: v1
kind: Service
metadata:
  name: web-crawler-api-loadbalancer
  labels:
    app: web-crawler-api
spec:
  type: LoadBalancer
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
  selector:
    app: web-crawler-api
