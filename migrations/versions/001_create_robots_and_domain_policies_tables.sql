-- Milestone 1.2: Robots.txt Compliance System

-- Robots.txt cache and rules
CREATE TABLE robots_cache (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,
    robots_txt TEXT,
    parsed_rules JSONB, -- Structured rules for faster processing
    user_agent_rules JSONB, -- Rules per user agent
    crawl_delay INTEGER, -- Delay in seconds
    sitemap_urls TEXT[], -- Array of sitemap URLs

    -- Metadata
    fetched_at TIMESTAMP DEFAULT NOW(),
    expires_at TIMESTAMP,
    fetch_attempts INTEGER DEFAULT 0,
    last_error TEXT,
    is_accessible BOOLEAN DEFAULT TRUE,

    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_robots_domain ON robots_cache(domain);
CREATE INDEX idx_robots_expires ON robots_cache(expires_at);

-- Domain crawling policies
CREATE TABLE domain_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    domain VARCHAR(255) UNIQUE NOT NULL,

    -- Crawling policies
    is_allowed BOOLEAN DEFAULT TRUE,
    crawl_delay INTEGER DEFAULT 1, -- Seconds between requests
    max_concurrent_requests INTEGER DEFAULT 1,
    respect_robots_txt BOOLEAN DEFAULT TRUE,

    -- Rate limiting
    requests_per_minute INTEGER DEFAULT 60,
    requests_per_hour INTEGER DEFAULT 3600,
    requests_per_day INTEGER DEFAULT 86400,

    -- Quality metrics
    success_rate FLOAT DEFAULT 1.0,
    avg_response_time INTEGER, -- Milliseconds
    last_successful_crawl TIMESTAMP,
    consecutive_failures INTEGER DEFAULT 0,

    -- Metadata
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_domain_policies_domain ON domain_policies(domain);
CREATE INDEX idx_domain_policies_allowed ON domain_policies(is_allowed);

-- Trigger for updated_at in robots_cache
CREATE OR REPLACE FUNCTION update_updated_at_column_robots_cache()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_robots_cache_updated_at BEFORE UPDATE
    ON robots_cache FOR EACH ROW EXECUTE FUNCTION update_updated_at_column_robots_cache();

-- Trigger for updated_at in domain_policies
CREATE OR REPLACE FUNCTION update_updated_at_column_domain_policies()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_domain_policies_updated_at BEFORE UPDATE
    ON domain_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column_domain_policies();

-- Ensure uuid-ossp extension is available (mentioned in Phase 0)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
