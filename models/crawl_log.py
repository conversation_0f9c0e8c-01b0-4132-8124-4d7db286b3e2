# models/crawl_log.py
from sqlalchemy import Column, String, Text, Integer, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
import uuid

from .base import Base


class CrawlLog(Base):
    """Model for crawl activity logging"""

    __tablename__ = "crawl_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    url = Column(Text, nullable=False)
    site_id = Column(UUID(as_uuid=True), ForeignKey("sites.id", ondelete="CASCADE"), index=True)
    status = Column(String(20), nullable=False, index=True)  # success, error, timeout, blocked
    http_status = Column(Integer)
    response_time_ms = Column(Integer)
    content_length = Column(Integer)
    error_message = Column(Text)
    user_agent = Column(Text)
    crawled_at = Column(DateTime, default=func.now(), nullable=False, index=True)

    # Relationships
    site = relationship("Site", back_populates="crawl_logs")

    def __repr__(self):
        return (
            f"<CrawlLog(url='{self.url}', status='{self.status}', http_status={self.http_status})>"
        )

    @classmethod
    def log_success(
        cls, session, url, site_id, http_status, response_time_ms, content_length, user_agent
    ):
        """Log a successful crawl"""
        log_entry = cls(
            url=url,
            site_id=site_id,
            status="success",
            http_status=http_status,
            response_time_ms=response_time_ms,
            content_length=content_length,
            user_agent=user_agent,
        )
        session.add(log_entry)
        return log_entry

    @classmethod
    def log_error(cls, session, url, site_id, error_message, user_agent, http_status=None):
        """Log a crawl error"""
        log_entry = cls(
            url=url,
            site_id=site_id,
            status="error",
            http_status=http_status,
            error_message=error_message,
            user_agent=user_agent,
        )
        session.add(log_entry)
        return log_entry
