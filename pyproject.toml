[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "web_crawler"
version = "0.1.0"
description = "Universal Web Directory - Next-generation web intelligence infrastructure"
authors = [{name = "<PERSON>", email = "m<PERSON><PERSON><PERSON>@gmail.com"}]
license = {text = "MIT"}
readme = "README.md"
requires-python = ">=3.12"
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.12",
]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "-ra",
    "--strict-markers",
    "--strict-config",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=xml",
    "--cov-report=html",
]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["."]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
    "*/archive/*",
    "setup.py",
    "*/venv/*",
    "*/.venv/*",
    "*/env/*",
    "*/.env/*",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.black]
line-length = 100
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
  | archive
)/
'''

[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*", "*/archive/*"]

[tool.flake8]
max-line-length = 100
extend-ignore = ["E203", "W503", "E501"]
exclude = [
    ".git",
    "__pycache__",
    "migrations",
    "archive",
    ".venv",
    "venv",
    "env",
    ".env",
]
per-file-ignores = [
    "__init__.py:F401",
    "tests/*:S101",
]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "migrations/",
    "archive/",
    "tests/",
]

[[tool.mypy.overrides]]
module = [
    "spacy.*",
    "transformers.*",
    "torch.*",
    "sklearn.*",
    "aiohttp.*",
    "beautifulsoup4.*",
    "bs4.*",
    "lxml.*",
    "psycopg2.*",
    "sqlalchemy.*",
]
ignore_missing_imports = true

[tool.bandit]
exclude_dirs = ["tests", "migrations", "archive"]
skips = ["B101", "B601"]  # Skip assert_used and shell_injection_process for tests

[tool.bandit.assert_used]
skips = ["*_test.py", "test_*.py"]
