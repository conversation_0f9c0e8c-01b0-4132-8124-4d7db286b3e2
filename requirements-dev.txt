# Development and Testing Dependencies
# Install with: pip install -r requirements-dev.txt

# Testing framework
pytest>=7.4.0
pytest-cov>=4.1.0 # For test coverage
pytest-asyncio>=0.21.0 # For asyncio support in pytest

# Code formatting and linting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0 # For import sorting
mypy>=1.7.0 # Static type checking
bandit>=1.7.5 # Security linter

# Coverage reporting
coverage>=7.0.0 # Explicitly listed, often a dep of pytest-cov
codecov>=2.1.13 # For uploading coverage reports to Codecov.io

# Mocking library (part of Python standard library for versions >= 3.3)
# unittest.mock # No separate install needed for Python 3.3+
