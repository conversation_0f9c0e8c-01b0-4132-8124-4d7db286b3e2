# Universal Web Directory - Web Crawler Requirements
# Python 3.12+ required
# Install with: pip install -r requirements.txt

# ============================================================================
# CORE WEB SCRAPING DEPENDENCIES
# ============================================================================

# HTTP requests library for web scraping
requests>=2.31.0

# HTML/XML parsing library
beautifulsoup4>=4.12.0

# FastAPI for API development
fastapi>=0.104.1

# ASGI server for FastAPI
uvicorn[standard]>=0.24.0

# Fast XML and HTML parser for BeautifulSoup
lxml>=4.9.0

# Alternative HTML parser (backup for lxml)
html5lib>=1.1

# ============================================================================
# DATA PROCESSING & STORAGE
# ============================================================================

# JSON handling (built-in, but listed for clarity)
# json - built-in module

# URL parsing and validation
urllib3>=2.0.0

# Data validation and settings management
pydantic>=2.5.0

# ============================================================================
# FUTURE NLP & AI CAPABILITIES
# ============================================================================

# Natural Language Processing
spacy>=3.7.0
transformers>=4.35.0
torch>=2.1.0
scikit-learn>=1.3.0 # Added for broader NLP model compatibility

# Text processing and analysis
# nltk>=3.8.0
# textblob>=0.17.0

# ============================================================================
# DATABASE & SEARCH (FUTURE)
# ============================================================================

# Vector database for semantic search
# chromadb>=0.4.0
# faiss-cpu>=1.7.0

# Traditional database support
sqlalchemy>=2.0.0
psycopg2-binary>=2.9.0  # PostgreSQL
# pymongo>=4.6.0          # MongoDB

# ============================================================================
# WEB CRAWLING ENHANCEMENTS
# ============================================================================

# Respect robots.txt (built-in Python module)
# robotparser is built into Python standard library

# User-Agent rotation and request management
fake-useragent>=1.4.0

# Rate limiting and request throttling
ratelimit>=2.2.0

# URL normalization and validation
furl>=2.1.0

# ============================================================================
# CONFIGURATION & LOGGING
# ============================================================================

# Configuration file management
pyyaml>=6.0.0
python-dotenv>=1.0.0

# Enhanced logging
loguru>=0.7.0

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================

# Code formatting and linting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0

# Type checking
mypy>=1.7.0

# Testing framework
pytest>=7.4.0
pytest-cov>=4.1.0

# ============================================================================
# KNOWLEDGE GRAPH
# ============================================================================

# Neo4j driver
neo4j>=5.20.0

# Graph analysis library
networkx>=3.0

# Community detection for NetworkX
python-louvain>=0.16

# ============================================================================
# UTILITIES & HELPERS
# ============================================================================

# Progress bars for long-running operations
tqdm>=4.66.0

# Date and time handling
python-dateutil>=2.8.0

# File system utilities
pathlib2>=2.3.0

# Command line interface
click>=8.1.0

# ============================================================================
# PERFORMANCE & MONITORING
# ============================================================================

# Memory profiling
memory-profiler>=0.61.0

# Performance monitoring
psutil>=5.9.0

# ============================================================================
# SECURITY & PRIVACY
# ============================================================================

# Secure HTTP requests
certifi>=2023.11.17

# URL security validation
validators>=0.22.0

# ============================================================================
# OPTIONAL: ADVANCED FEATURES (COMMENTED OUT)
# ============================================================================

# Selenium for JavaScript-heavy sites
# selenium>=4.15.0
# webdriver-manager>=4.0.0

# Scrapy framework (alternative approach)
# scrapy>=2.11.0

# Async HTTP client for high-performance crawling
aiohttp>=3.9.0
# asyncio - built-in module

# Image processing for content analysis
# pillow>=10.1.0
# opencv-python>=4.8.0

# PDF processing
# pypdf2>=3.0.0
# pdfplumber>=0.10.0

# Excel/CSV processing
# pandas>=2.1.0
# openpyxl>=3.1.0

# ============================================================================
# NOTES
# ============================================================================

# 1. Commented dependencies are for future development phases
# 2. Version numbers are minimum requirements for Python 3.12 compatibility
# 3. Some packages may require system-level dependencies (e.g., lxml needs libxml2)
# 4. For production deployment, consider pinning exact versions
# 5. Use 'pip install -r requirements.txt' to install all dependencies
# 6. For development: 'pip install -r requirements.txt --upgrade'

# ============================================================================
# INSTALLATION NOTES
# ============================================================================

# For conda users (recommended):
# conda activate web_crawler
# pip install -r requirements.txt

# For system-wide installation:
# pip install -r requirements.txt

# For development with all optional dependencies:
# Uncomment desired packages above and run:
# pip install -r requirements.txt
