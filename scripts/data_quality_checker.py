# This script will be responsible for running checks on the collected data
# to ensure its quality, consistency, and integrity.

import logging

# Configure basic logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# --- Placeholder Data Access Functions ---
# In a real application, these would interact with your database or data store.


def get_recent_pages(limit=100):
    """Fetches recently crawled pages from the database."""
    from config.database import DatabaseConfig
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker
    from models.pages import Page

    try:
        config = DatabaseConfig()
        engine = create_engine(config.connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()

        # Fetch recent pages from database
        pages = session.query(Page).order_by(Page.scraped_at.desc()).limit(limit).all()

        result = []
        for page in pages:
            result.append(
                {
                    "url": page.url,
                    "title": page.title,
                    "word_count": len(page.content_text.split()) if page.content_text else 0,
                    "http_status": page.http_status,
                    "language": page.detected_language,
                }
            )

        session.close()
        logging.info(f"Fetched {len(result)} recent pages for quality check.")
        return result

    except Exception as e:
        logging.error(f"Error fetching recent pages: {str(e)}")
        return []


def get_site_statistics(domain):
    """Fetches statistics for a given site from the database."""
    from config.database import DatabaseConfig
    from sqlalchemy import create_engine, func
    from sqlalchemy.orm import sessionmaker
    from models.pages import Page
    from models.sites import Site

    try:
        config = DatabaseConfig()
        engine = create_engine(config.connection_string)
        Session = sessionmaker(bind=engine)
        session = Session()

        # Get site statistics from database
        site = session.query(Site).filter(Site.domain == domain).first()
        if not site:
            logging.warning(f"Site {domain} not found in database.")
            session.close()
            return None

        # Calculate statistics
        pages = session.query(Page).filter(Page.site_id == site.id).all()
        total_pages = len(pages)

        if total_pages > 0:
            word_counts = [len(p.content_text.split()) if p.content_text else 0 for p in pages]
            avg_word_count = sum(word_counts) / len(word_counts)
            error_4xx_count = len([p for p in pages if 400 <= p.http_status < 500])
            error_rate_4xx = error_4xx_count / total_pages
        else:
            avg_word_count = 0
            error_rate_4xx = 0

        result = {
            "domain": domain,
            "total_pages": total_pages,
            "avg_word_count": avg_word_count,
            "error_rate_4xx": error_rate_4xx,
        }

        session.close()
        logging.info(f"Fetched statistics for site: {domain}")
        return result

    except Exception as e:
        logging.error(f"Error fetching site statistics for {domain}: {str(e)}")
        return None


# --- Data Quality Checks ---


def check_missing_titles(pages):
    """Checks for pages with missing titles."""
    missing_title_pages = [page["url"] for page in pages if not page.get("title")]
    if missing_title_pages:
        logging.warning(
            f"Data Quality: Found {len(missing_title_pages)} pages with missing titles. Examples: {missing_title_pages[:3]}"
        )
    return len(missing_title_pages)


def check_low_word_count(pages, min_word_count=50):
    """Checks for pages with unusually low word count (for successfully crawled pages)."""
    low_word_count_pages = [
        page["url"]
        for page in pages
        if page.get("http_status") == 200 and page.get("word_count", 0) < min_word_count
    ]
    if low_word_count_pages:
        logging.warning(
            f"Data Quality: Found {len(low_word_count_pages)} pages with word count < {min_word_count}. Examples: {low_word_count_pages[:3]}"
        )
    return len(low_word_count_pages)


def check_http_error_rates(pages):
    """Checks the proportion of pages with HTTP errors."""
    total_pages = len(pages)
    if total_pages == 0:
        return 0, 0

    error_4xx_count = sum(1 for page in pages if page.get("http_status", 0) // 100 == 4)
    error_5xx_count = sum(1 for page in pages if page.get("http_status", 0) // 100 == 5)

    rate_4xx = error_4xx_count / total_pages
    rate_5xx = error_5xx_count / total_pages

    if rate_4xx > 0.1:  # Example threshold
        logging.warning(f"Data Quality: High 4xx error rate: {rate_4xx:.2%}")
    if rate_5xx > 0.05:  # Example threshold
        logging.warning(f"Data Quality: High 5xx error rate: {rate_5xx:.2%}")
    return rate_4xx, rate_5xx


def check_language_consistency(pages):
    """Checks for unexpected language codes or high variance if undesirable."""
    # This is a very basic check. More sophisticated checks might look at language
    # consistency within a single site or compare declared vs. detected language.
    languages = [page.get("language") for page in pages if page.get("language")]
    unique_languages = set(languages)
    if len(unique_languages) > 10:  # Arbitrary threshold for many languages
        logging.warning(f"Data Quality: High variety of languages detected: {unique_languages}")
    # Check for common invalid codes (very simplistic)
    invalid_codes = [
        lang for lang in unique_languages if lang and (len(lang) > 3 or not lang.islower())
    ]
    if invalid_codes:
        logging.warning(f"Data Quality: Potential invalid language codes: {invalid_codes}")
    return len(unique_languages)


def run_all_checks():
    logging.info("Starting Data Quality Checks...")

    recent_pages_sample = get_recent_pages(limit=200)  # Get a sample of data

    if not recent_pages_sample:
        logging.info("No data found to perform quality checks.")
        return

    check_missing_titles(recent_pages_sample)
    check_low_word_count(recent_pages_sample)
    check_http_error_rates(recent_pages_sample)
    check_language_consistency(recent_pages_sample)

    # Example of a site-specific check
    # site_stats = get_site_statistics("example.com")
    # if site_stats["error_rate_4xx"] > 0.15:
    #    logging.warning(f"Data Quality: Site {site_stats['domain']} has high 4xx error rate: {site_stats['error_rate_4xx']:.2%}")

    logging.info("Data Quality Checks Completed.")


if __name__ == "__main__":
    # This script could be scheduled to run periodically (e.g., daily).
    # run_all_checks()
    print("Placeholder for data_quality_checker.py. Run run_all_checks() to see example logging.")
    pass
