#!/usr/bin/env python3
"""
Phase 4 Pre-Cleanup Script

This script performs comprehensive cleanup before beginning Phase 4:
1. Archives deprecated files
2. Removes temporary/generated files
3. Cleans up development artifacts
4. Prepares for Phase 4: Production & Deployment
"""

import logging
import shutil
import os
from pathlib import Path
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Phase4Cleanup:
    """Comprehensive cleanup for Phase 4 preparation"""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.archive_dir = self.project_root / "archive"
        self.phase3_archive = self.archive_dir / "phase3_cleanup"
        self.cleanup_summary = []

    def create_archive_structure(self):
        """Create archive directory structure"""
        logger.info("🗂️  Creating archive directory structure...")
        
        self.phase3_archive.mkdir(parents=True, exist_ok=True)
        
        # Create subdirectories for different types of archived files
        (self.phase3_archive / "generated_reports").mkdir(exist_ok=True)
        (self.phase3_archive / "development_artifacts").mkdir(exist_ok=True)
        (self.phase3_archive / "temporary_files").mkdir(exist_ok=True)
        
        logger.info(f"✅ Archive structure created at: {self.phase3_archive}")

    def archive_file(self, source_path: Path, category: str, reason: str):
        """Archive a file to the appropriate category"""
        if not source_path.exists():
            logger.warning(f"⚠️  File not found: {source_path}")
            return False
        
        # Determine destination
        dest_dir = self.phase3_archive / category
        dest_path = dest_dir / source_path.name
        
        # Handle name conflicts
        counter = 1
        while dest_path.exists():
            stem = source_path.stem
            suffix = source_path.suffix
            dest_path = dest_dir / f"{stem}_{counter}{suffix}"
            counter += 1
        
        try:
            shutil.move(str(source_path), str(dest_path))
            logger.info(f"📦 Archived: {source_path.name} → {category}/ ({reason})")
            self.cleanup_summary.append(f"Archived {source_path.name}: {reason}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to archive {source_path}: {e}")
            return False

    def remove_file(self, file_path: Path, reason: str):
        """Remove a file completely"""
        if not file_path.exists():
            logger.warning(f"⚠️  File not found: {file_path}")
            return False
        
        try:
            file_path.unlink()
            logger.info(f"🗑️  Removed: {file_path.name} ({reason})")
            self.cleanup_summary.append(f"Removed {file_path.name}: {reason}")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to remove {file_path}: {e}")
            return False

    def cleanup_generated_reports(self):
        """Archive generated reports and security scans"""
        logger.info("🔍 Cleaning up generated reports...")
        
        files_to_archive = [
            (self.project_root / "bandit-report.json", "Security scan report from Phase 3"),
        ]
        
        for file_path, reason in files_to_archive:
            if file_path.exists():
                self.archive_file(file_path, "generated_reports", reason)

    def cleanup_development_artifacts(self):
        """Archive development-specific files"""
        logger.info("🛠️  Cleaning up development artifacts...")
        
        files_to_archive = [
            (self.project_root / "setup.py", "Placeholder setup.py file"),
            (self.project_root / "web_crawler.code-workspace", "VS Code workspace file"),
        ]
        
        for file_path, reason in files_to_archive:
            if file_path.exists():
                self.archive_file(file_path, "development_artifacts", reason)

    def cleanup_pycache(self):
        """Remove __pycache__ directories"""
        logger.info("🧹 Cleaning up Python cache directories...")
        
        pycache_dirs = list(self.project_root.rglob("__pycache__"))
        
        for cache_dir in pycache_dirs:
            if cache_dir.is_dir() and "archive" not in str(cache_dir):
                try:
                    shutil.rmtree(cache_dir)
                    logger.info(f"🗑️  Removed cache: {cache_dir.relative_to(self.project_root)}")
                    self.cleanup_summary.append(f"Removed cache directory: {cache_dir.name}")
                except Exception as e:
                    logger.error(f"❌ Failed to remove {cache_dir}: {e}")

    def cleanup_coverage_files(self):
        """Remove coverage files if they exist"""
        logger.info("📊 Cleaning up coverage files...")
        
        coverage_files = [
            self.project_root / ".coverage",
            self.project_root / "htmlcov",
            self.project_root / "coverage.xml",
        ]
        
        for coverage_file in coverage_files:
            if coverage_file.exists():
                if coverage_file.is_dir():
                    try:
                        shutil.rmtree(coverage_file)
                        logger.info(f"🗑️  Removed coverage directory: {coverage_file.name}")
                        self.cleanup_summary.append(f"Removed coverage directory: {coverage_file.name}")
                    except Exception as e:
                        logger.error(f"❌ Failed to remove {coverage_file}: {e}")
                else:
                    self.remove_file(coverage_file, "Coverage report file")

    def verify_essential_files(self):
        """Verify that essential files are still present"""
        logger.info("✅ Verifying essential files...")
        
        essential_files = [
            "README.md",
            "requirements.txt",
            "requirements-prod.txt",
            "requirements-dev.txt",
            "Dockerfile",
            "docker-compose.yml",
            "pyproject.toml",
            "api/main.py",
            "database/schema.sql",
        ]
        
        missing_files = []
        for file_path in essential_files:
            full_path = self.project_root / file_path
            if not full_path.exists():
                missing_files.append(file_path)
                logger.error(f"❌ Essential file missing: {file_path}")
            else:
                logger.info(f"✅ Essential file present: {file_path}")
        
        if missing_files:
            logger.error(f"❌ Missing essential files: {missing_files}")
            return False
        else:
            logger.info("✅ All essential files are present")
            return True

    def create_cleanup_documentation(self):
        """Create documentation of cleanup actions"""
        logger.info("📝 Creating cleanup documentation...")
        
        doc_content = f"""# Phase 4 Pre-Cleanup Report

**Cleanup Date**: {datetime.now().isoformat()}
**Phase**: Preparation for Phase 4: Production & Deployment

## Summary of Actions

Total actions performed: {len(self.cleanup_summary)}

## Detailed Actions

"""
        
        for i, action in enumerate(self.cleanup_summary, 1):
            doc_content += f"{i}. {action}\n"
        
        doc_content += f"""

## Archive Structure

Files have been organized in the following archive structure:
- `archive/phase3_cleanup/generated_reports/` - Security scans and reports
- `archive/phase3_cleanup/development_artifacts/` - Development-specific files
- `archive/phase3_cleanup/temporary_files/` - Temporary and cache files

## Next Steps

The project is now ready for Phase 4: Production & Deployment.
All deprecated files have been archived and the codebase is clean.

## Files Preserved

All essential files for production deployment have been preserved:
- Application code (api/, models/, services/)
- Configuration files (config/)
- Database schema and migrations
- Docker and Kubernetes deployment files
- Requirements and dependency files
- Documentation and roadmap files
"""
        
        doc_path = self.project_root / "docs" / "phase4_preparation_cleanup.md"
        doc_path.write_text(doc_content)
        
        logger.info(f"📄 Cleanup documentation created: {doc_path}")

    def run_cleanup(self):
        """Execute the complete cleanup process"""
        logger.info("🚀 Starting Phase 4 pre-cleanup...")
        logger.info("=" * 60)
        
        # Create archive structure
        self.create_archive_structure()
        
        # Perform cleanup operations
        self.cleanup_generated_reports()
        self.cleanup_development_artifacts()
        self.cleanup_pycache()
        self.cleanup_coverage_files()
        
        # Verify essential files
        if not self.verify_essential_files():
            logger.error("❌ Cleanup failed: Essential files missing")
            return False
        
        # Create documentation
        self.create_cleanup_documentation()
        
        # Summary
        logger.info("=" * 60)
        logger.info(f"✅ Phase 4 pre-cleanup completed successfully!")
        logger.info(f"📊 Total actions: {len(self.cleanup_summary)}")
        logger.info(f"📁 Archive location: {self.phase3_archive}")
        logger.info("🎯 Project is ready for Phase 4: Production & Deployment")
        
        return True


def main():
    """Main cleanup function"""
    cleanup = Phase4Cleanup()
    success = cleanup.run_cleanup()
    
    if success:
        logger.info("\n🎉 Cleanup completed successfully!")
        return 0
    else:
        logger.error("\n❌ Cleanup failed!")
        return 1


if __name__ == "__main__":
    import sys
    sys.exit(main())
