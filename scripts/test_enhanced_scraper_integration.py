#!/usr/bin/env python3
"""
Test script to verify Enhanced Content Scraper integration with comprehensive database
"""

import asyncio
import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.database import DatabaseConfig
from services.crawler.content_scraper import EnhancedContentScraper
from services.crawler.rate_limiter import AdaptiveRateLimiter
from services.crawler.robots_handler import RobotsHandler
from models.sites import Site
from models.pages import Page
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import hashlib

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


async def test_enhanced_scraper_database_integration():
    """Test enhanced content scraper with database storage"""

    # Initialize database connection
    db_config = DatabaseConfig()
    engine = create_engine(db_config.connection_string)
    Session = sessionmaker(bind=engine)
    session = Session()

    try:
        # Initialize services
        rate_limiter = AdaptiveRateLimiter(session, worker_id="integration_test")
        robots_handler = RobotsHandler(session)
        content_scraper = EnhancedContentScraper(
            session=session, rate_limiter=rate_limiter, robots_handler=robots_handler
        )

        # Test URL
        test_url = "https://httpbin.org/html"
        logger.info(f"Testing enhanced scraper with URL: {test_url}")

        # Scrape the page
        result = await content_scraper.scrape_page(test_url)

        if "error" in result:
            logger.error(f"Scraping failed: {result['error']}")
            return False

        logger.info(f"Successfully scraped page: {result['title']}")
        logger.info(f"Content length: {result['content_length']} bytes")
        logger.info(f"Word count: {result['word_count']} words")
        logger.info(f"Internal links: {result['internal_links_count']}")
        logger.info(f"External links: {result['external_links_count']}")
        logger.info(f"Images: {result['image_count']}")

        # Now test database storage with comprehensive schema
        domain = "httpbin.org"

        # Find or create site
        site = session.query(Site).filter_by(domain=domain).first()
        if not site:
            site = Site(
                domain=domain,
                title="HTTPBin Test Site",
                description="HTTP Request & Response Service",
                ssl_enabled=True,
                status="active",
            )
            session.add(site)
            session.commit()
            session.refresh(site)
            logger.info(f"Created new site: {domain}")

        # Create page with comprehensive metadata
        url_hash = hashlib.sha256(test_url.encode()).hexdigest()

        # Check if page already exists
        existing_page = session.query(Page).filter_by(url_hash=url_hash).first()
        if existing_page:
            logger.info("Page already exists, updating...")
            page = existing_page
        else:
            page = Page(site_id=site.id, url=test_url, url_hash=url_hash)
            session.add(page)

        # Update page with scraped data
        page.title = result["title"]
        page.meta_description = result.get("meta_description", "")
        page.content_text = result["content_text"]
        page.content_html = result["content_html"]
        page.content_length = result["content_length"]
        page.word_count = result["word_count"]
        page.heading_h1 = result["heading_h1"]
        page.heading_h2 = result["heading_h2"]
        page.heading_h3 = result["heading_h3"]
        page.internal_links_count = result["internal_links_count"]
        page.external_links_count = result["external_links_count"]
        page.image_count = result["image_count"]
        page.content_hash = result["content_hash"]
        page.http_status = result["http_status"]
        page.content_type = result["content_type"]
        page.last_modified = result.get("last_modified")
        page.etag = result.get("etag")
        page.last_crawled = datetime.utcnow()

        session.commit()
        session.refresh(page)

        logger.info(f"Successfully stored page data with comprehensive schema")
        logger.info(f"Page ID: {page.id}")
        logger.info(f"Site ID: {site.id}")

        # Verify the data was stored correctly
        stored_page = session.query(Page).filter_by(id=page.id).first()
        assert stored_page is not None, "Page should be stored in database"
        assert stored_page.title == result["title"], "Title should match"
        assert stored_page.content_hash == result["content_hash"], "Content hash should match"
        assert stored_page.word_count == result["word_count"], "Word count should match"
        assert len(stored_page.heading_h1) == len(result["heading_h1"]), "H1 headings should match"

        logger.info("✅ Enhanced Content Scraper database integration test PASSED")
        return True

    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        return False
    finally:
        session.close()


async def main():
    """Main test function"""
    logger.info("🚀 Starting Enhanced Content Scraper Database Integration Test")
    logger.info("=" * 70)

    success = await test_enhanced_scraper_database_integration()

    logger.info("=" * 70)
    if success:
        logger.info("🎉 Enhanced Content Scraper Database Integration: SUCCESS ✅")
    else:
        logger.info("❌ Enhanced Content Scraper Database Integration: FAILED")

    return success


if __name__ == "__main__":
    asyncio.run(main())
