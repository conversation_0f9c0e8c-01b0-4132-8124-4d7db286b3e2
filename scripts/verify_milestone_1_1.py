#!/usr/bin/env python3
"""
Milestone 1.1 Verification Script - Advanced URL Frontier System
Comprehensive verification of all Milestone 1.1 success criteria
"""

import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.url_frontier import URLFrontier
from services.crawler.url_frontier_service import URLFrontierService
from config.database import SessionLocal
from sqlalchemy import text

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


def verify_database_schema():
    """Verify the advanced URL frontier database schema"""
    print("🔍 Verifying Database Schema...")

    try:
        with SessionLocal() as session:
            # Check if url_frontier table exists with correct columns
            result = session.execute(
                text(
                    """
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'url_frontier'
                ORDER BY ordinal_position;
            """
                )
            )

            columns = {row.column_name: row.data_type for row in result}

            required_columns = {
                "id": "uuid",
                "url": "character varying",
                "url_hash": "character varying",
                "domain": "character varying",
                "subdomain": "character varying",
                "path": "text",
                "query_params": "jsonb",
                "status": "character varying",
                "priority": "integer",
                "source_type": "character varying",
                "discovered_at": "timestamp without time zone",
                "scheduled_for": "timestamp without time zone",
                "last_crawled": "timestamp without time zone",
                "next_crawl": "timestamp without time zone",
                "crawl_attempts": "integer",
                "max_attempts": "integer",
                "error_message": "text",
                "error_type": "character varying",
                "http_status": "integer",
                "content_type": "character varying",
                "content_length": "bigint",
                "last_modified": "timestamp without time zone",
                "etag": "character varying",
                "worker_id": "character varying",
                "created_at": "timestamp without time zone",
                "updated_at": "timestamp without time zone",
            }

            missing_columns = []
            for col, expected_type in required_columns.items():
                if col not in columns:
                    missing_columns.append(col)
                elif expected_type not in columns[col]:
                    logger.warning(
                        f"Column {col} has type {columns[col]}, expected {expected_type}"
                    )

            if missing_columns:
                print(f"❌ Missing columns: {missing_columns}")
                return False

            # Check indexes
            result = session.execute(
                text(
                    """
                SELECT indexname FROM pg_indexes 
                WHERE tablename = 'url_frontier';
            """
                )
            )

            indexes = [row.indexname for row in result]
            required_indexes = [
                "idx_url_frontier_status",
                "idx_url_frontier_priority",
                "idx_url_frontier_domain",
                "idx_url_frontier_scheduled_for",
                "idx_url_frontier_worker_coordination",
                "idx_url_frontier_next_crawl",
                "idx_url_frontier_url_hash",
            ]

            missing_indexes = [idx for idx in required_indexes if idx not in indexes]
            if missing_indexes:
                print(f"❌ Missing indexes: {missing_indexes}")
                return False

            print("✅ Database schema verification passed")
            return True

    except Exception as e:
        print(f"❌ Database schema verification failed: {e}")
        return False


def verify_url_frontier_model():
    """Verify the URLFrontier model functionality"""
    print("\n🔍 Verifying URLFrontier Model...")

    try:
        # Test URL parsing
        test_url = "https://example.com/path/to/page?param1=value1&param2=value2"
        url_entry = URLFrontier(url=test_url, priority=8, source_type="test")

        # Verify URL parsing
        assert url_entry.url == test_url
        assert url_entry.url_hash is not None and len(url_entry.url_hash) == 64
        assert url_entry.domain == "example.com"
        assert url_entry.path == "/path/to/page"
        assert url_entry.query_params == {"param1": ["value1"], "param2": ["value2"]}
        assert url_entry.priority == 8
        assert url_entry.source_type == "test"

        print("✅ URLFrontier model verification passed")
        return True

    except Exception as e:
        print(f"❌ URLFrontier model verification failed: {e}")
        return False


def verify_url_frontier_service():
    """Verify the URLFrontierService functionality"""
    print("\n🔍 Verifying URLFrontierService...")

    try:
        with URLFrontierService() as service:
            # Test adding URLs
            test_urls = [
                {"url": "https://verify1.com", "priority": 9, "source_type": "verification"},
                {"url": "https://verify2.com/page", "priority": 7, "source_type": "verification"},
                {
                    "url": "https://verify3.com/api?test=true",
                    "priority": 5,
                    "source_type": "verification",
                },
            ]

            result = service.add_urls_batch(test_urls)
            assert result["added"] == 3
            assert result["failed"] == 0

            # Test getting frontier stats
            stats = service.get_frontier_stats()
            assert "total_urls" in stats
            assert "ready_for_crawl" in stats
            assert "status_distribution" in stats
            assert stats["total_urls"] > 0

            # Test getting next URLs for crawling
            next_urls = service.get_next_urls(limit=2, worker_id="verification_worker")
            assert len(next_urls) <= 2

            # Test marking URLs as crawled
            if next_urls:
                url_to_mark = next_urls[0]
                success = service.mark_crawled(
                    url_id=str(url_to_mark.id),
                    success=True,
                    http_status=200,
                    content_type="text/html",
                    content_length=1024,
                )
                assert success is True

            # Test cleanup functions
            cleaned = service.cleanup_stale_workers(timeout_hours=0.01)
            assert isinstance(cleaned, int)

            print("✅ URLFrontierService verification passed")
            return True

    except Exception as e:
        print(f"❌ URLFrontierService verification failed: {e}")
        return False


def verify_distributed_crawling():
    """Verify distributed crawling capabilities"""
    print("\n🔍 Verifying Distributed Crawling...")

    try:
        with URLFrontierService() as service:
            # Add test URLs
            test_urls = [
                {
                    "url": f"https://distributed-test-{i}.com",
                    "priority": 5,
                    "source_type": "distributed_test",
                }
                for i in range(5)
            ]
            service.add_urls_batch(test_urls)

            # Simulate multiple workers
            worker1_urls = service.get_next_urls(limit=2, worker_id="worker_1")
            worker2_urls = service.get_next_urls(limit=2, worker_id="worker_2")

            # Verify no overlap (distributed locking)
            worker1_ids = {str(url.id) for url in worker1_urls}
            worker2_ids = {str(url.id) for url in worker2_urls}
            overlap = worker1_ids.intersection(worker2_ids)

            assert len(overlap) == 0, f"Workers got overlapping URLs: {overlap}"

            # Verify worker assignment
            for url in worker1_urls:
                assert url.worker_id == "worker_1"
                assert url.status == "crawling"

            for url in worker2_urls:
                assert url.worker_id == "worker_2"
                assert url.status == "crawling"

            print("✅ Distributed crawling verification passed")
            return True

    except Exception as e:
        print(f"❌ Distributed crawling verification failed: {e}")
        return False


def verify_url_deduplication():
    """Verify URL deduplication using SHA-256 hashing"""
    print("\n🔍 Verifying URL Deduplication...")

    try:
        with URLFrontierService() as service:
            # Try to add the same URL twice
            test_url = "https://dedup-test.com/page"

            # First addition should succeed
            success1 = service.add_url(test_url, priority=5, source_type="dedup_test")
            assert success1 is True

            # Second addition should fail (duplicate)
            success2 = service.add_url(test_url, priority=5, source_type="dedup_test")
            assert success2 is False

            # Verify hash-based deduplication
            url_entry1 = URLFrontier(url=test_url)
            url_entry2 = URLFrontier(url=test_url)
            assert url_entry1.url_hash == url_entry2.url_hash

            print("✅ URL deduplication verification passed")
            return True

    except Exception as e:
        print(f"❌ URL deduplication verification failed: {e}")
        return False


def verify_error_handling():
    """Verify comprehensive error handling and retry logic"""
    print("\n🔍 Verifying Error Handling...")

    try:
        with URLFrontierService() as service:
            # Add a test URL
            test_url = "https://error-test.com"
            service.add_url(test_url, priority=5, source_type="error_test")

            # Get the URL for processing
            urls = service.get_next_urls(limit=1, worker_id="error_worker")
            assert len(urls) == 1

            url_entry = urls[0]

            # Mark as failed (should retry)
            success = service.mark_crawled(
                url_id=str(url_entry.id),
                success=False,
                error_msg="Connection timeout",
                http_status=None,
            )
            assert success is True

            # Verify retry logic
            with SessionLocal() as session:
                updated_entry = session.query(URLFrontier).filter_by(id=url_entry.id).first()
                assert updated_entry.status == "pending"  # Should be retried
                assert updated_entry.error_message == "Connection timeout"
                assert updated_entry.error_type == "network_error"

            print("✅ Error handling verification passed")
            return True

    except Exception as e:
        print(f"❌ Error handling verification failed: {e}")
        return False


def main():
    """Run all verification tests"""
    print("🧪 MILESTONE 1.1 VERIFICATION - Advanced URL Frontier System")
    print("=" * 70)

    tests = [
        verify_database_schema,
        verify_url_frontier_model,
        verify_url_frontier_service,
        verify_distributed_crawling,
        verify_url_deduplication,
        verify_error_handling,
    ]

    passed = 0
    failed = 0

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"Test {test.__name__} failed with exception: {e}")
            failed += 1

    print(f"\n📊 VERIFICATION RESULTS:")
    print(f"   ✅ Passed: {passed}")
    print(f"   ❌ Failed: {failed}")
    print(f"   📈 Success Rate: {passed/(passed+failed)*100:.1f}%")

    if failed == 0:
        print("\n🎯 MILESTONE 1.1 VERIFICATION SUCCESSFUL!")
        print("   ✅ Advanced URL frontier schema deployed")
        print("   ✅ Enhanced URLFrontier model with URL parsing")
        print("   ✅ URLFrontierService with distributed crawling support")
        print("   ✅ Comprehensive error handling and metadata tracking")
        print("   ✅ Worker coordination and cleanup mechanisms")
        print("   ✅ URL deduplication with SHA-256 hashing")
        print("   ✅ Enterprise-grade queue system operational")
        return True
    else:
        print(f"\n❌ MILESTONE 1.1 VERIFICATION FAILED!")
        print(f"   {failed} test(s) failed. Please review the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
