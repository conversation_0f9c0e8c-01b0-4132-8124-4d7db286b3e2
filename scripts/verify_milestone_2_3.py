#!/usr/bin/env python3
"""
Verification script for Milestone 2.3: Testing & Quality Assurance Framework
Tests all components of the testing infrastructure, CI/CD pipeline, and quality gates
"""

import asyncio
import sys
import os
import logging
import subprocess
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)


class Milestone23Verifier:
    def __init__(self):
        self.total_tests = 0
        self.passed_tests = 0
        self.project_root = Path(__file__).parent.parent

    def log_test(self, test_name, passed, details=""):
        """Log test results"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            logger.info(f"✅ {test_name} - {details} - PASSED")
        else:
            logger.info(f"❌ {test_name} - {details} - FAILED")

    async def verify_test_infrastructure(self):
        """Test 1: Verify testing infrastructure exists"""
        logger.info("🔍 Test 1: Verifying Testing Infrastructure")

        try:
            # Check for conftest.py
            conftest_path = self.project_root / "tests" / "conftest.py"
            if conftest_path.exists():
                self.log_test("conftest.py", True, "Test configuration file exists")
            else:
                self.log_test("conftest.py", False, "Test configuration file missing")

            # Check for test files
            test_files = ["tests/test_content_scraper.py", "tests/test_nlp_analyzer.py"]

            for test_file in test_files:
                test_path = self.project_root / test_file
                if test_path.exists():
                    self.log_test(f"Test file {test_file}", True, "Test file exists")
                else:
                    self.log_test(f"Test file {test_file}", False, "Test file missing")

        except Exception as e:
            self.log_test("Testing Infrastructure", False, f"Error: {str(e)}")

    async def verify_development_dependencies(self):
        """Test 2: Verify development dependencies"""
        logger.info("🔍 Test 2: Verifying Development Dependencies")

        try:
            # Check requirements-dev.txt
            req_dev_path = self.project_root / "requirements-dev.txt"
            if req_dev_path.exists():
                self.log_test("requirements-dev.txt", True, "Development requirements file exists")

                # Check key dependencies
                with open(req_dev_path, "r") as f:
                    content = f.read()

                key_deps = ["pytest", "black", "flake8", "mypy", "bandit", "pytest-cov"]
                for dep in key_deps:
                    if dep in content:
                        self.log_test(
                            f"Dependency {dep}", True, f"{dep} found in requirements-dev.txt"
                        )
                    else:
                        self.log_test(
                            f"Dependency {dep}", False, f"{dep} missing from requirements-dev.txt"
                        )
            else:
                self.log_test(
                    "requirements-dev.txt", False, "Development requirements file missing"
                )

        except Exception as e:
            self.log_test("Development Dependencies", False, f"Error: {str(e)}")

    async def verify_ci_cd_pipeline(self):
        """Test 3: Verify CI/CD pipeline configuration"""
        logger.info("🔍 Test 3: Verifying CI/CD Pipeline Configuration")

        try:
            # Check GitHub Actions workflow
            ci_path = self.project_root / ".github" / "workflows" / "ci.yml"
            if ci_path.exists():
                self.log_test("GitHub Actions CI", True, "CI workflow file exists")

                with open(ci_path, "r") as f:
                    content = f.read()

                # Check for key CI components
                ci_components = ["postgres:", "pytest", "black --check", "flake8", "mypy", "bandit"]
                for component in ci_components:
                    if component in content:
                        self.log_test(
                            f"CI Component {component}", True, f"{component} found in CI config"
                        )
                    else:
                        self.log_test(
                            f"CI Component {component}",
                            False,
                            f"{component} missing from CI config",
                        )
            else:
                self.log_test("GitHub Actions CI", False, "CI workflow file missing")

        except Exception as e:
            self.log_test("CI/CD Pipeline", False, f"Error: {str(e)}")

    async def verify_code_quality_tools(self):
        """Test 4: Verify code quality tools can run"""
        logger.info("🔍 Test 4: Verifying Code Quality Tools")

        try:
            # Test if tools are installed and can run
            tools = {
                "black": ["black", "--version"],
                "flake8": ["flake8", "--version"],
                "mypy": ["mypy", "--version"],
                "bandit": ["bandit", "--version"],
            }

            for tool_name, command in tools.items():
                try:
                    result = subprocess.run(command, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        self.log_test(
                            f"Tool {tool_name}", True, f"{tool_name} is installed and working"
                        )
                    else:
                        self.log_test(f"Tool {tool_name}", False, f"{tool_name} command failed")
                except (subprocess.TimeoutExpired, FileNotFoundError):
                    self.log_test(f"Tool {tool_name}", False, f"{tool_name} not found or timeout")

        except Exception as e:
            self.log_test("Code Quality Tools", False, f"Error: {str(e)}")

    async def verify_test_execution(self):
        """Test 5: Verify tests can be executed"""
        logger.info("🔍 Test 5: Verifying Test Execution")

        try:
            # Try to run pytest with dry-run to check if tests are discoverable
            result = subprocess.run(
                ["python", "-m", "pytest", "--collect-only", "-q"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                # Count discovered tests
                lines = result.stdout.split("\n")
                test_count = len([line for line in lines if "::test_" in line])
                self.log_test("Test Discovery", True, f"Found {test_count} discoverable tests")
            else:
                self.log_test("Test Discovery", False, f"Test discovery failed: {result.stderr}")

        except Exception as e:
            self.log_test("Test Execution", False, f"Error: {str(e)}")

    async def verify_test_coverage_setup(self):
        """Test 6: Verify test coverage configuration"""
        logger.info("🔍 Test 6: Verifying Test Coverage Setup")

        try:
            # Check if pytest-cov is available
            result = subprocess.run(
                ["python", "-c", 'import pytest_cov; print("pytest-cov available")'],
                capture_output=True,
                text=True,
                timeout=10,
            )

            if result.returncode == 0:
                self.log_test("Coverage Tool", True, "pytest-cov is available")
            else:
                self.log_test("Coverage Tool", False, "pytest-cov not available")

            # Check if coverage configuration exists (in pyproject.toml, setup.cfg, or .coveragerc)
            config_files = [
                self.project_root / "pyproject.toml",
                self.project_root / "setup.cfg",
                self.project_root / ".coveragerc",
            ]

            coverage_config_found = any(f.exists() for f in config_files)
            if coverage_config_found:
                self.log_test("Coverage Config", True, "Coverage configuration file found")
            else:
                self.log_test(
                    "Coverage Config", False, "No coverage configuration file found (optional)"
                )

        except Exception as e:
            self.log_test("Test Coverage Setup", False, f"Error: {str(e)}")

    async def run_all_tests(self):
        """Run all verification tests"""
        logger.info("🧪 Starting Milestone 2.3: Testing & Quality Assurance Framework Verification")
        logger.info("=" * 80)

        # Run all tests
        await self.verify_test_infrastructure()
        await self.verify_development_dependencies()
        await self.verify_ci_cd_pipeline()
        await self.verify_code_quality_tools()
        await self.verify_test_execution()
        await self.verify_test_coverage_setup()

        # Print summary
        logger.info("=" * 80)
        logger.info(f"📊 VERIFICATION SUMMARY")
        logger.info(f"Total Tests: {self.total_tests}")
        logger.info(f"Passed: {self.passed_tests}")
        logger.info(f"Failed: {self.total_tests - self.passed_tests}")
        logger.info(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")

        if self.passed_tests == self.total_tests:
            logger.info("🎉 ALL TESTS PASSED - Milestone 2.3 is FULLY IMPLEMENTED!")
        else:
            logger.info("⚠️ Some tests failed - Milestone 2.3 needs additional work")


if __name__ == "__main__":
    verifier = Milestone23Verifier()
    asyncio.run(verifier.run_all_tests())
