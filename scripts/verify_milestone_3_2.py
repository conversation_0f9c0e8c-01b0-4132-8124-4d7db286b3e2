#!/usr/bin/env python3
"""
Verification script for Milestone 3.2: FastAPI REST API Development

This script verifies that the FastAPI REST API has been fully implemented
according to the specifications in the development roadmap.

Success Criteria:
1. ✅ FastAPI application structure with proper metadata and documentation
2. ✅ Authentication system with API key verification
3. ✅ All required API endpoints implemented and functional
4. ✅ Proper Pydantic models for request/response validation
5. ✅ Error handling with appropriate HTTP status codes
6. ✅ OpenAPI documentation generation (/docs and /redoc)
7. ✅ Service integration (SearchService, SiteAnalyzer, ContentService, TrendAnalyzer)
8. ✅ CORS and security middleware configuration
9. ✅ Production-ready uvicorn configuration
10. ✅ Comprehensive API testing and validation
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path

import requests

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# API Configuration
API_BASE_URL = "http://localhost:8000"
TEST_API_KEY = "test_api_key"
INVALID_API_KEY = "invalid_key"


def test_api_health():
    """Test 1: API Health Check"""
    logger.info("🔍 Testing API health check...")
    try:
        response = requests.get(f"{API_BASE_URL}/")
        assert response.status_code == 200
        data = response.json()
        assert data["service"] == "Universal Web Directory API"
        assert data["version"] == "1.0.0"
        assert data["status"] == "operational"
        assert "timestamp" in data
        assert data["documentation"] == "/docs"
        logger.info("✅ API health check passed")
        return True
    except Exception as e:
        logger.error(f"❌ API health check failed: {e}")
        return False


def test_authentication():
    """Test 2: Authentication System"""
    logger.info("🔍 Testing authentication system...")
    try:
        # Test valid API key
        headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
        response = requests.get(f"{API_BASE_URL}/trending", headers=headers)
        assert response.status_code == 200
        logger.info("✅ Valid API key authentication passed")

        # Test invalid API key
        headers = {"Authorization": f"Bearer {INVALID_API_KEY}"}
        response = requests.get(f"{API_BASE_URL}/trending", headers=headers)
        assert response.status_code == 401
        data = response.json()
        assert "Invalid API key" in data.get("error", "")
        logger.info("✅ Invalid API key rejection passed")

        # Test missing API key
        response = requests.get(f"{API_BASE_URL}/trending")
        assert response.status_code == 403
        logger.info("✅ Missing API key rejection passed")

        return True
    except Exception as e:
        logger.error(f"❌ Authentication test failed: {e}")
        return False


def test_search_endpoint():
    """Test 3: Search Endpoint"""
    logger.info("🔍 Testing search endpoint...")
    try:
        headers = {
            "Authorization": f"Bearer {TEST_API_KEY}",
            "Content-Type": "application/json",
        }
        payload = {
            "query": "python web scraping",
            "search_type": "hybrid",
            "limit": 3,
            "offset": 0,
        }
        response = requests.post(f"{API_BASE_URL}/search", headers=headers, json=payload)
        assert response.status_code == 200
        data = response.json()
        assert data["query"] == "python web scraping"
        assert data["search_type"] == "hybrid"
        assert "total_results" in data
        assert "results" in data
        assert "response_time_ms" in data
        assert "pagination" in data
        assert len(data["results"]) <= 3
        logger.info("✅ Search endpoint passed")
        return True
    except Exception as e:
        logger.error(f"❌ Search endpoint test failed: {e}")
        return False


def test_site_analysis_endpoint():
    """Test 4: Site Analysis Endpoint"""
    logger.info("🔍 Testing site analysis endpoint...")
    try:
        headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
        response = requests.get(f"{API_BASE_URL}/sites/example.com/analysis", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["domain"] == "example.com"
        assert "title" in data
        assert "description" in data
        assert "authority_score" in data
        assert "page_count" in data
        assert "technology_stack" in data
        assert "content_categories" in data
        assert "language_distribution" in data
        logger.info("✅ Site analysis endpoint passed")
        return True
    except Exception as e:
        logger.error(f"❌ Site analysis endpoint test failed: {e}")
        return False


def test_content_endpoint():
    """Test 5: Content Endpoint"""
    logger.info("🔍 Testing content endpoint...")
    try:
        headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
        response = requests.get(
            f"{API_BASE_URL}/content/test-page-123?include_analysis=true", headers=headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["page_id"] == "test-page-123"
        assert "url" in data
        assert "title" in data
        assert "raw_html" in data
        assert "text_content" in data
        assert "metadata" in data
        assert "nlp_analysis" in data
        logger.info("✅ Content endpoint passed")
        return True
    except Exception as e:
        logger.error(f"❌ Content endpoint test failed: {e}")
        return False


def test_trending_endpoint():
    """Test 6: Trending Topics Endpoint"""
    logger.info("🔍 Testing trending topics endpoint...")
    try:
        headers = {"Authorization": f"Bearer {TEST_API_KEY}"}
        response = requests.get(f"{API_BASE_URL}/trending?timeframe=24h&limit=3", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["timeframe"] == "24h"
        assert "trending_topics" in data
        assert "generated_at" in data
        assert len(data["trending_topics"]) <= 3
        logger.info("✅ Trending topics endpoint passed")
        return True
    except Exception as e:
        logger.error(f"❌ Trending topics endpoint test failed: {e}")
        return False


def test_openapi_documentation():
    """Test 7: OpenAPI Documentation"""
    logger.info("🔍 Testing OpenAPI documentation...")
    try:
        # Test Swagger UI
        response = requests.get(f"{API_BASE_URL}/docs")
        assert response.status_code == 200
        assert "swagger-ui" in response.text.lower()
        logger.info("✅ Swagger UI documentation available")

        # Test ReDoc
        response = requests.get(f"{API_BASE_URL}/redoc")
        assert response.status_code == 200
        assert "redoc" in response.text.lower()
        logger.info("✅ ReDoc documentation available")

        # Test OpenAPI JSON schema
        response = requests.get(f"{API_BASE_URL}/openapi.json")
        assert response.status_code == 200
        schema = response.json()
        assert schema["info"]["title"] == "Universal Web Directory API"
        assert schema["info"]["version"] == "1.0.0"
        assert "paths" in schema
        logger.info("✅ OpenAPI JSON schema available")

        return True
    except Exception as e:
        logger.error(f"❌ OpenAPI documentation test failed: {e}")
        return False


def test_service_integration():
    """Test 8: Service Integration"""
    logger.info("🔍 Testing service integration...")
    try:
        # Import and test service classes
        from services.auth.api_auth import APIAuthService
        from services.search.search_service import SearchService
        from services.analysis.site_analyzer import SiteAnalyzer
        from services.content.content_service import ContentService
        from services.intelligence.trend_analyzer import TrendAnalyzer

        # Test service instantiation
        auth_service = APIAuthService()
        search_service = SearchService()
        site_analyzer = SiteAnalyzer()
        content_service = ContentService()
        trend_analyzer = TrendAnalyzer()

        logger.info("✅ All services can be imported and instantiated")
        return True
    except Exception as e:
        logger.error(f"❌ Service integration test failed: {e}")
        return False


def test_fastapi_app_structure():
    """Test 9: FastAPI Application Structure"""
    logger.info("🔍 Testing FastAPI application structure...")
    try:
        from api.main import app

        # Test app metadata
        assert app.title == "Universal Web Directory API"
        assert app.version == "1.0.0"
        assert app.docs_url == "/docs"
        assert app.redoc_url == "/redoc"

        # Test middleware configuration
        middleware_classes = []
        for middleware in app.user_middleware:
            if hasattr(middleware, "cls"):
                middleware_classes.append(middleware.cls.__name__)
            else:
                middleware_classes.append(type(middleware).__name__)

        # Check if CORS and TrustedHost middleware are configured
        # Note: In FastAPI, middleware may be wrapped, so we check for their presence
        assert len(app.user_middleware) >= 2  # Should have at least CORS and TrustedHost

        logger.info("✅ FastAPI application structure verified")
        return True
    except Exception as e:
        logger.error(f"❌ FastAPI application structure test failed: {e}")
        return False


def main():
    """Run all verification tests"""
    logger.info("🚀 Starting Milestone 3.2 verification...")
    logger.info("=" * 60)

    tests = [
        ("API Health Check", test_api_health),
        ("Authentication System", test_authentication),
        ("Search Endpoint", test_search_endpoint),
        ("Site Analysis Endpoint", test_site_analysis_endpoint),
        ("Content Endpoint", test_content_endpoint),
        ("Trending Topics Endpoint", test_trending_endpoint),
        ("OpenAPI Documentation", test_openapi_documentation),
        ("Service Integration", test_service_integration),
        ("FastAPI App Structure", test_fastapi_app_structure),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n📋 Running: {test_name}")
        if test_func():
            passed += 1
        time.sleep(0.5)  # Brief pause between tests

    logger.info("\n" + "=" * 60)
    logger.info(f"📊 MILESTONE 3.2 VERIFICATION RESULTS")
    logger.info(f"✅ Passed: {passed}/{total}")
    logger.info(f"❌ Failed: {total - passed}/{total}")

    if passed == total:
        logger.info("🎉 ALL TESTS PASSED! Milestone 3.2 is fully implemented.")
        return True
    else:
        logger.error("💥 Some tests failed. Please review the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
