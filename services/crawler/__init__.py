# This file makes the 'crawler' directory within 'services' a Python sub-package.

"""
Services for crawling and processing web content.

This package includes:
- URL Discovery: Tools for finding new URLs to crawl. (Migrated via LegacyMigrator)
- Content Scraping: Tools for fetching and parsing web page content. (Migrated via LegacyMigrator)
- Robots.txt Handling: Utilities for respecting website crawl policies. (Future module)
- Rate Limiting: Mechanisms to manage crawl speed and politeness. (Future module)
- Legacy Migration: Tools for integrating older crawling components.
"""

import logging

# Import the LegacyMigrator to make it accessible via services.crawler.LegacyMigrator
from .legacy_migrator import LegacyMigrator

# The existing url_discovery.py and content_scraper.py in this directory are basic scripts
# and are conceptually superseded by the logic now callable through LegacyMigrator,
# which uses the more advanced versions from the `core` package.
# They might be removed or refactored in later stages.
# from .url_discovery import urls as simple_legacy_url_script_urls
# from .content_scraper import data as simple_legacy_scraper_data


# Define what is exported when 'from services.crawler import *' is used.
__all__ = [
    "LegacyMigrator",
    # Other services like URLDiscoveryService, ContentScrapingService would be added here
    # once they are refactored out of or built on top of LegacyMigrator's capabilities.
]

logger = logging.getLogger(__name__)
logger.info("services.crawler package initialized. LegacyMigrator is available.")
