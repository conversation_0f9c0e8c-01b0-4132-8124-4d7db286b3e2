# services/crawler/rate_limiter.py
import time
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Optional
from collections import defaultdict, deque
import threading
import logging  # Added for logging
from sqlalchemy.orm import Session  # Added for type hinting
from sqlalchemy.sql import func  # Added for database functions
import datetime as dt  # For timezone-aware UTC

# Assuming models will be in these locations
from models.rate_limit_tracking import RateLimitTracking
from models.request_history import RequestHistory

logger = logging.getLogger(__name__)


class AdaptiveRateLimiter:
    """Advanced rate limiter with adaptive delays and distributed coordination"""

    def __init__(self, session: Session, worker_id: str):  # Added type hint for session
        self.session = session
        self.worker_id = worker_id
        # self.local_cache = {}  # Local rate limit cache - Consider if needed with DB backend
        # self.request_history = defaultdict(lambda: deque(maxlen=1000)) # Will be stored in DB
        self.lock = (
            threading.Lock()
        )  # To protect access to shared resources if any, or DB session if not thread-safe by default

        # Default policies
        self.default_delay_ms = 1000  # milliseconds
        self.max_delay_ms = 30000  # milliseconds
        self.min_delay_ms = 100  # milliseconds
        self.adaptive_factor_increase = 1.2  # Increase delay by 20% on failure
        self.adaptive_factor_decrease = 0.9  # Decrease delay by 10% on success
        self.failure_threshold = (
            3  # Number of consecutive failures to trigger significant delay increase
        )
        self.max_consecutive_failures = 10  # Max consecutive failures before applying max_delay

    async def wait_if_needed(self, domain: str) -> float:
        """Wait if needed before making request, returns actual delay in seconds"""
        # Implementation for _get_rate_limit_state, _calculate_delay, _update_rate_limit_state
        # will be added in the next steps.
        # For now, let's ensure the structure is in place.

        actual_delay_seconds = 0.0
        with self.lock:  # Ensure thread safety for DB operations
            rate_state = self._get_rate_limit_state(domain)

            current_time = dt.datetime.utcnow()
            required_delay_ms = self._calculate_delay(rate_state, current_time)

            if required_delay_ms > 0:
                actual_delay_seconds = required_delay_ms / 1000.0
                logger.info(
                    f"Rate limiting domain '{domain}': Waiting for {actual_delay_seconds:.2f}s. Next allowed: {rate_state.next_allowed_request if rate_state else 'N/A'}"
                )
                await asyncio.sleep(actual_delay_seconds)

            self._update_rate_limit_state_before_request(
                domain, rate_state
            )  # Pass current rate_state to update

        return actual_delay_seconds

    def _get_rate_limit_state(self, domain: str) -> Optional[RateLimitTracking]:
        """Retrieve rate limit state for a domain from the database."""
        try:
            # Prioritize worker-specific entry, fall back to general domain entry if needed,
            # though current schema implies worker_id is part of the unique key with domain.
            # For simplicity, we'll assume worker_id is always relevant for this instance.
            rate_state = (
                self.session.query(RateLimitTracking)
                .filter_by(domain=domain, worker_id=self.worker_id)
                .first()
            )

            if not rate_state:
                # If no specific entry for this worker, check for a general domain entry (if design allows)
                # For now, we stick to worker-specific or create new.
                # rate_state = self.session.query(RateLimitTracking).filter_by(domain=domain, worker_id=None).first()
                # if not rate_state:
                # Create a new record if none exists for this domain/worker combination
                logger.info(
                    f"No rate limit state found for domain '{domain}', worker '{self.worker_id}'. Creating new entry."
                )
                new_state = RateLimitTracking(
                    domain=domain,
                    worker_id=self.worker_id,
                    current_delay_ms=self.default_delay_ms,
                    next_allowed_request=dt.datetime.utcnow(),  # Allow first request immediately
                )
                self.session.add(new_state)
                self.session.commit()  # Commit immediately to make it available for current request
                return new_state

            return rate_state
        except Exception as e:
            logger.error(f"Error getting rate limit state for domain '{domain}': {e}")
            self.session.rollback()
            # Fallback: if DB error, use default delay and allow request to proceed with caution
            # This prevents rate limiter from becoming a single point of failure for crawling.
            # However, this might violate rate limits if DB is down for long.
            # A more robust solution might involve a local cache or stricter error handling.
            return RateLimitTracking(
                domain=domain,
                worker_id=self.worker_id,  # Temporary object, not persisted
                current_delay_ms=self.default_delay_ms,
                next_allowed_request=dt.datetime.utcnow(),
            )

    def _calculate_delay(
        self, rate_state: Optional[RateLimitTracking], current_time: dt.datetime
    ) -> int:
        """Calculate the required delay in milliseconds based on the current rate state."""
        if not rate_state:
            # Should not happen if _get_rate_limit_state creates a default entry
            logger.warning(
                f"Rate state is None in _calculate_delay for domain. Using default delay: {self.default_delay_ms}ms"
            )
            return self.default_delay_ms

        # Ensure next_allowed_request is not None
        if rate_state.next_allowed_request is None:
            # This might happen if a new record was just created and not fully populated,
            # or if there's an issue with the data. Default to allowing the request.
            logger.warning(
                f"rate_state.next_allowed_request is None for domain {rate_state.domain}. Allowing request immediately."
            )
            return 0

        # Both datetimes should be timezone-naive for consistency
        time_until_next_allowed = (rate_state.next_allowed_request - current_time).total_seconds()

        if time_until_next_allowed <= 0:
            return 0  # No delay needed

        # Return delay in milliseconds
        return int(time_until_next_allowed * 1000)

    def _update_rate_limit_state_before_request(
        self, domain: str, rate_state: Optional[RateLimitTracking]
    ):
        """Update rate limit state in the database before making a request."""
        if not rate_state:
            # This case should ideally be handled by _get_rate_limit_state creating an entry.
            # If it still occurs, log an error and create a new state.
            logger.error(
                f"Rate state is None in _update_rate_limit_state_before_request for domain '{domain}'. This should not happen."
            )
            rate_state = RateLimitTracking(
                domain=domain,
                worker_id=self.worker_id,
                current_delay_ms=self.default_delay_ms,
                next_allowed_request=dt.datetime.utcnow()
                + timedelta(milliseconds=self.default_delay_ms),
            )
            self.session.add(rate_state)

        try:
            current_time = dt.datetime.utcnow()
            rate_state.last_request_at = current_time

            # Update request counters (requests_last_minute, etc.)
            # This is a simplified counter update. A more robust implementation might use
            # time-windowed counters (e.g., Redis with EXPIRE or a dedicated table).
            # For now, we'll increment. Proper decrementing or resetting of these counters
            # based on time windows is complex and might be out of scope for this direct implementation.
            # The schema has requests_last_minute, requests_last_hour, requests_last_day.
            # These would typically be updated by a separate process or more complex logic.
            # Here, we'll just mark that a request is happening.
            # A full implementation of these counters would require tracking individual request timestamps.

            # For simplicity in this step, we're not fully implementing the periodic counters.
            # We'll focus on last_request_at and next_allowed_request.
            # The actual periodic counting (last minute/hour/day) is often handled
            # by aggregating RequestHistory or using a more specialized time-series approach.

            # The next_allowed_request will be updated by record_request_result based on success/failure.
            # For now, we just ensure last_request_at is set.

            self.session.commit()
        except Exception as e:
            logger.error(
                f"Error updating rate limit state for domain '{domain}' before request: {e}"
            )
            self.session.rollback()

    def record_request_result(
        self,
        domain: str,
        url: str,
        success: bool,
        response_time_ms: int,
        status_code: Optional[int] = None,
        content_length: Optional[int] = None,
        error_type: Optional[str] = None,
        error_message: Optional[str] = None,
    ):
        """Record request result for adaptive rate limiting and history."""
        # Record time as close to actual event as possible
        # If this method is called immediately after request, dt.datetime.now(dt.UTC) is fine for completed_at
        # requested_at should ideally be timestamped just before the actual network call.
        # For now, we'll use current time for both if not passed in.
        # Assuming 'requested_at' is when we decided to make the request (after any delays)
        # and 'completed_at' is when we got the result.

        # If this method is called right after the request, this is a good completed_at time.
        current_time = dt.datetime.now(dt.UTC)
        # requested_at should ideally be passed in. If not, using current_time is an approximation.
        # For simplicity, if record_request_result is called immediately after the HTTP request,
        # response_time_ms can be used to estimate requested_at.
        # requested_at_approx = current_time - timedelta(milliseconds=response_time_ms)

        # Let's assume for now that the caller might pass requested_at if available,
        # otherwise we use current_time as a fallback for requested_at for the history entry.
        # This is a simplification. A more robust system would timestamp more accurately.

        # For the purpose of this class, let's use current_time as completed_at
        # and also as requested_at for the DB entry if not otherwise specified.
        # The AdaptiveRateLimiter itself mainly cares about when the *next* request is allowed.

        completed_at_ts = current_time
        # If 'requested_at' is not passed, we use 'completed_at_ts' as a placeholder.
        # A better approach would be to have the caller provide the actual request initiation time.
        # For now, the DB schema for RequestHistory has `requested_at` defaulting to `func.now()`.
        # So, if we don't set it, it will be the DB transaction time.
        # Let's set it to `completed_at_ts - response_time_ms` for better accuracy.
        requested_at_ts = completed_at_ts - timedelta(milliseconds=response_time_ms)

        with self.lock:  # Ensure thread safety for DB operations
            try:
                # 1. Log to request_history
                history_entry = RequestHistory(
                    domain=domain,
                    url=url,
                    worker_id=self.worker_id,
                    method="GET",  # Assuming GET, can be parameterized if needed
                    status_code=status_code,
                    response_time_ms=response_time_ms,
                    content_length=content_length,
                    success=success,
                    error_type=error_type,
                    error_message=error_message,
                    requested_at=requested_at_ts,  # This should be when the request was initiated
                    completed_at=completed_at_ts,  # This should be when the response was received
                )
                self.session.add(history_entry)

                # 2. Fetch current rate_limit_tracking state
                rate_state = self._get_rate_limit_state(domain)
                if not rate_state:
                    # This should not happen due to _get_rate_limit_state's logic, but as a safeguard:
                    logger.error(
                        f"Could not find or create rate_state for domain '{domain}' in record_request_result."
                    )
                    # Avoid further errors by not proceeding with adaptive adjustments if state is missing
                    self.session.commit()  # Commit history entry at least
                    return

                # 3. Call _adjust_delay_adaptive
                self._adjust_delay_adaptive(rate_state, success)

                # 4. Update success rate and other counters in rate_limit_tracking
                # Simplified success rate: (total successes) / (total requests)
                # This requires tracking total requests and successes.
                # For now, we'll use a rolling average approach if possible or simplify.
                # The schema has `success_rate`, let's try to update it.
                # A more robust success rate would look at a window of recent requests.

                # Increment total requests for the relevant time windows (minute, hour, day)
                # This is complex to do accurately without a proper time-series or windowed counting.
                # We will placeholder this logic for now, as it's often handled by an aggregation job.
                # For example, updating `requests_last_minute` accurately requires knowing which requests
                # fell exactly in the last minute.

                # Let's try a simple update for success_rate, assuming it's a rolling/recent success rate.
                # For a more accurate rolling success rate, we'd query recent RequestHistory.
                # Simplified approach:
                # If success, slightly increase success_rate, if failure, slightly decrease.
                # This is a very naive way to update success_rate.
                # A better way: Query last N requests from RequestHistory for this domain/worker.
                # For now, we'll just update based on current success.
                # current_total_requests = (rate_state.requests_last_hour or 0) + 1 # Example
                # current_successes = int((rate_state.success_rate or 1.0) * (current_total_requests -1))
                # if success:
                #     current_successes +=1
                # rate_state.success_rate = float(current_successes) / current_total_requests if current_total_requests > 0 else 1.0

                # The schema's `success_rate` field in `rate_limit_tracking` is best updated
                # by an aggregate function over `request_history` periodically or via triggers.
                # For this direct implementation, we'll adjust it based on a simple smoothing factor.
                smoothing_factor = 0.1  # How much the new result affects the current success rate
                current_success_value = 1.0 if success else 0.0
                if rate_state.success_rate is None:  # Initialize if null
                    rate_state.success_rate = current_success_value
                else:
                    rate_state.success_rate = (rate_state.success_rate * (1 - smoothing_factor)) + (
                        current_success_value * smoothing_factor
                    )

                # 5. Commit changes
                self.session.commit()
                logger.debug(
                    f"Recorded request for {url}. Success: {success}. New delay for {domain}: {rate_state.current_delay_ms}ms. Next: {rate_state.next_allowed_request}"
                )

            except Exception as e:
                logger.error(f"Error recording request result for {url}: {e}")
                self.session.rollback()

    def _adjust_delay_adaptive(self, rate_state: RateLimitTracking, success: bool):
        """Adjusts the current_delay_ms adaptively based on request success/failure."""
        if success:
            rate_state.consecutive_failures = 0
            # Decrease delay, but not below min_delay_ms
            new_delay_ms = int(rate_state.current_delay_ms * self.adaptive_factor_decrease)
            rate_state.current_delay_ms = max(self.min_delay_ms, new_delay_ms)
            # logger.debug(f"Success for {rate_state.domain}. New delay: {rate_state.current_delay_ms}ms")
        else:
            rate_state.consecutive_failures += 1
            # Increase delay, but not above max_delay_ms
            if rate_state.consecutive_failures >= self.failure_threshold:
                # More significant increase after multiple failures
                increase_factor = self.adaptive_factor_increase * (
                    1 + (rate_state.consecutive_failures - self.failure_threshold) * 0.1
                )
                new_delay_ms = int(rate_state.current_delay_ms * increase_factor)
                logger.warning(
                    f"{rate_state.consecutive_failures} consecutive failures for {rate_state.domain}. Increasing delay significantly."
                )
            else:
                new_delay_ms = int(rate_state.current_delay_ms * self.adaptive_factor_increase)

            rate_state.current_delay_ms = min(self.max_delay_ms, new_delay_ms)

            if rate_state.consecutive_failures >= self.max_consecutive_failures:
                logger.error(
                    f"Max consecutive failures ({self.max_consecutive_failures}) reached for {rate_state.domain}. Setting delay to max: {self.max_delay_ms}ms."
                )
                rate_state.current_delay_ms = self.max_delay_ms

        # Update next_allowed_request based on the new delay
        # Use timezone-naive datetime for database compatibility
        rate_state.next_allowed_request = dt.datetime.utcnow() + timedelta(
            milliseconds=rate_state.current_delay_ms
        )
        # logger.info(f"Adaptive delay for {rate_state.domain}: {rate_state.current_delay_ms}ms, Next allowed: {rate_state.next_allowed_request}")
