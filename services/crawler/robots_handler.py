# services/crawler/robots_handler.py
import urllib.robotparser
import requests
from urllib.parse import urljoin, urlparse
from typing import Optional, List, Dict
import json
from datetime import datetime, timedelta
from models.robots_cache import RobotsCache
from models.domain_policies import DomainPolicy


class RobotsHandler:
    """Comprehensive robots.txt compliance system with database-backed caching"""

    def __init__(self, session, user_agent: str = "UniversalWebDirectory/1.0"):
        self.session = session
        self.user_agent = user_agent
        self.cache_duration = timedelta(days=1)  # Cache robots.txt for 1 day

    def can_crawl(self, url: str) -> bool:
        """Check if URL can be crawled according to robots.txt"""
        domain = urlparse(url).netloc
        robots_data = self._get_robots_data(domain)

        if not robots_data or not robots_data.is_accessible:
            return True  # If robots.txt not accessible, assume allowed

        # Parse robots.txt rules
        rp = urllib.robotparser.RobotFileParser()
        rp.set_url(f"https://{domain}/robots.txt")
        if robots_data.robots_txt:
            rp.read()
            return rp.can_fetch(self.user_agent, url)

        return True

    def get_crawl_delay(self, domain: str) -> int:
        """Get crawl delay for domain from robots.txt"""
        robots_data = self._get_robots_data(domain)
        if robots_data and robots_data.crawl_delay:
            return robots_data.crawl_delay

        # Check domain policies
        policy = self._get_domain_policy(domain)
        return policy.crawl_delay if policy else 1

    def get_sitemaps(self, domain: str) -> List[str]:
        """Get sitemap URLs from robots.txt"""
        robots_data = self._get_robots_data(domain)
        return robots_data.sitemap_urls if robots_data else []

    def _get_robots_data(self, domain: str):
        """Get cached robots.txt data or fetch if expired"""
        # Check cache
        cached = self.session.query(RobotsCache).filter_by(domain=domain).first()

        if cached and cached.expires_at > datetime.utcnow():
            return cached

        # Fetch new robots.txt
        return self._fetch_robots_txt(domain)

    def _get_domain_policy(self, domain: str):
        """Get domain policy from database"""
        return self.session.query(DomainPolicy).filter_by(domain=domain).first()

    def _fetch_robots_txt(self, domain: str):
        """Fetch and parse robots.txt"""
        robots_url = f"https://{domain}/robots.txt"

        try:
            response = requests.get(robots_url, timeout=10, headers={"User-Agent": self.user_agent})

            if response.status_code == 200:
                robots_txt = response.text
                parsed_rules = self._parse_robots_txt(robots_txt)

                # Update or create cache entry
                cached = self.session.query(RobotsCache).filter_by(domain=domain).first()
                if not cached:
                    cached = RobotsCache(domain=domain)

                cached.robots_txt = robots_txt
                cached.parsed_rules = json.dumps(parsed_rules)
                cached.crawl_delay = parsed_rules.get("crawl_delay", 1)
                cached.sitemap_urls = parsed_rules.get("sitemaps", [])
                cached.fetched_at = datetime.utcnow()
                cached.expires_at = datetime.utcnow() + self.cache_duration
                cached.is_accessible = True

                self.session.add(cached)
                self.session.commit()

                return cached

        except Exception as e:
            # Mark as inaccessible
            cached = self.session.query(RobotsCache).filter_by(domain=domain).first()
            if not cached:
                cached = RobotsCache(domain=domain)

            cached.is_accessible = False
            cached.last_error = str(e)
            cached.fetch_attempts = (cached.fetch_attempts or 0) + 1
            cached.expires_at = datetime.utcnow() + timedelta(hours=6)

            self.session.add(cached)
            self.session.commit()

            return cached

    def _parse_robots_txt(self, robots_txt: str) -> Dict:
        """Parse robots.txt content into structured data"""
        rules = {"user_agents": {}, "sitemaps": [], "crawl_delay": None}

        current_user_agent = None

        for line in robots_txt.split("\n"):
            line = line.strip()
            if not line or line.startswith("#"):
                continue

            if ":" in line:
                key, value = line.split(":", 1)
                key = key.strip().lower()
                value = value.strip()

                if key == "user-agent":
                    current_user_agent = value
                    if current_user_agent not in rules["user_agents"]:
                        rules["user_agents"][current_user_agent] = {
                            "allow": [],
                            "disallow": [],
                            "crawl_delay": None,
                        }

                elif key == "disallow" and current_user_agent:
                    rules["user_agents"][current_user_agent]["disallow"].append(value)

                elif key == "allow" and current_user_agent:
                    rules["user_agents"][current_user_agent]["allow"].append(value)

                elif key == "crawl-delay" and current_user_agent:
                    try:
                        delay = int(value)
                        rules["user_agents"][current_user_agent]["crawl_delay"] = delay
                        if not rules["crawl_delay"]:
                            rules["crawl_delay"] = delay
                    except ValueError:
                        pass

                elif key == "sitemap":
                    rules["sitemaps"].append(value)

        return rules
