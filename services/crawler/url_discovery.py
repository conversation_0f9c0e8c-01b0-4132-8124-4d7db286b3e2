#!/usr/bin/env python3
"""
URL Discovery Service - Migrated from core/url_discovery.py
Part of the new services architecture for Milestone 0.4
"""

import logging
from typing import List, Optional, Dict, Any
from core.url_discovery import URLDiscoveryEngine

logger = logging.getLogger(__name__)


class URLDiscoveryService:
    """
    Service wrapper for URL Discovery functionality.
    Migrated from core/url_discovery.py with enhanced error handling and configuration.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize the URL Discovery Service"""
        self.config = config or {}
        self.engine = URLDiscoveryEngine()
        logger.info("URLDiscoveryService initialized")

    def add_seed_urls(self, urls: List[str], priority: int = 5) -> Dict[str, Any]:
        """
        Add multiple seed URLs to the frontier

        Args:
            urls: List of URLs to add
            priority: Priority level (1-10, higher = more important)

        Returns:
            Dict with results summary
        """
        results = {"added": 0, "failed": 0, "errors": []}

        for url in urls:
            try:
                success = self.engine.add_seed_url(url, priority=priority)
                if success:
                    results["added"] += 1
                else:
                    results["failed"] += 1
            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"Error adding {url}: {str(e)}")
                logger.error(f"Failed to add seed URL {url}: {e}")

        return results

    def get_next_urls(self, limit: int = 10) -> List[Any]:
        """Get next URLs to crawl from the frontier"""
        try:
            return self.engine.get_next_urls(limit=limit)
        except Exception as e:
            logger.error(f"Error getting next URLs: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """Get frontier statistics"""
        try:
            return self.engine.get_frontier_stats()
        except Exception as e:
            logger.error(f"Error getting stats: {e}")
            return {}

    def discover_urls_from_page(self, url: str, content: str) -> List[str]:
        """Extract URLs from page content"""
        try:
            return self.engine.extract_urls_from_content(url, content)
        except Exception as e:
            logger.error(f"Error discovering URLs from {url}: {e}")
            return []
