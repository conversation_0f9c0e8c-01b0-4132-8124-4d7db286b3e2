from neo4j import GraphDatabase
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime
import networkx as nx

class KnowledgeGraphBuilder:
    """Build and maintain knowledge graphs from crawled data"""

    def __init__(self, neo4j_uri: str, neo4j_user: str, neo4j_password: str):
        self.driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        self.logger = logging.getLogger(__name__)

        # NetworkX graph for in-memory analysis
        self.analysis_graph = nx.DiGraph()

    async def build_site_relationships(self, site_data: Dict) -> Dict:
        """Build relationships between sites based on linking patterns"""
        try:
            with self.driver.session() as session:
                # Create or update site node
                site_query = \"\"\"
                MERGE (s:Site {domain: $domain})
                SET s.title = $title,
                    s.authority_score = $authority_score,
                    s.category = $category,
                    s.language = $language,
                    s.last_updated = datetime()
                RETURN s.domain as domain
                \"\"\"

                result = session.run(site_query, {
                    'domain': site_data['domain'],
                    'title': site_data.get('title'),
                    'authority_score': site_data.get('authority_score', 0.0),
                    'category': site_data.get('category'),
                    'language': site_data.get('language', 'en')
                })

                # Build outbound link relationships
                if 'outbound_links' in site_data:
                    await self._create_site_link_relationships(
                        site_data['domain'],
                        site_data['outbound_links']
                    )

                return {'success': True, 'domain': site_data['domain']}

        except Exception as e:
            self.logger.error(f"Failed to build site relationships: {e}")
            return {'success': False, 'error': str(e)}

    async def build_entity_relationships(self, page_data: Dict) -> Dict:
        """Build entity co-occurrence and relationship networks"""
        try:
            with self.driver.session() as session:
                page_url = page_data['url']
                entities = page_data.get('entities', [])

                # Create page node
                page_query = \"\"\"
                MERGE (p:Page {url: $url})
                SET p.title = $title,
                    p.content_hash = $content_hash,
                    p.word_count = $word_count,
                    p.language = $language,
                    p.last_updated = datetime()
                RETURN p.url as url
                \"\"\"

                session.run(page_query, {
                    'url': page_url,
                    'title': page_data.get('title'),
                    'content_hash': page_data.get('content_hash'),
                    'word_count': page_data.get('word_count', 0),
                    'language': page_data.get('language', 'en')
                })

                # Create entity nodes and relationships
                for entity in entities:
                    await self._create_entity_relationships(page_url, entity)

                # Build entity co-occurrence relationships
                await self._build_entity_cooccurrence(page_url, entities)

                return {'success': True, 'entities_processed': len(entities)}

        except Exception as e:
            self.logger.error(f"Failed to build entity relationships: {e}")
            return {'success': False, 'error': str(e)}

    async def _create_entity_relationships(self, page_url: str, entity: Dict):
        """Create entity nodes and page-entity relationships"""
        with self.driver.session() as session:
            entity_query = \"\"\"
            MERGE (e:Entity {canonical_name: $canonical_name})
            SET e.entity_type = $entity_type,
                e.confidence_score = $confidence_score,
                e.frequency_count = coalesce(e.frequency_count, 0) + $frequency,
                e.wikipedia_id = $wikipedia_id,
                e.wikidata_id = $wikidata_id,
                e.last_updated = datetime()

            WITH e
            MATCH (p:Page {url: $page_url})
            MERGE (p)-[r:MENTIONS]->(e)
            SET r.frequency = $frequency,
                r.context = $context,
                r.confidence = $confidence_score
            \"\"\"

            session.run(entity_query, {
                'canonical_name': entity['canonical_name'],
                'entity_type': entity['entity_type'],
                'confidence_score': entity.get('confidence_score', 0.0),
                'frequency': entity.get('frequency', 1),
                'wikipedia_id': entity.get('wikipedia_id'),
                'wikidata_id': entity.get('wikidata_id'),
                'page_url': page_url,
                'context': entity.get('context', '')
            })

    async def _build_entity_cooccurrence(self, page_url: str, entities: List[Dict]):
        """Build co-occurrence relationships between entities on the same page"""
        if len(entities) < 2:
            return

        with self.driver.session() as session:
            # Create co-occurrence relationships for all entity pairs
            for i, entity1 in enumerate(entities):
                for entity2 in entities[i+1:]:
                    cooccurrence_query = \"\"\"
                    MATCH (e1:Entity {canonical_name: $entity1_name})
                    MATCH (e2:Entity {canonical_name: $entity2_name})
                    MERGE (e1)-[r:CO_OCCURS_WITH]-(e2)
                    SET r.frequency = coalesce(r.frequency, 0) + 1,
                        r.last_seen = datetime(),
                        r.strength = coalesce(r.strength, 0.0) + $strength
                    \"\"\"

                    # Calculate co-occurrence strength based on entity types and confidence
                    strength = min(
                        entity1.get('confidence_score', 0.5),
                        entity2.get('confidence_score', 0.5)
                    )

                    session.run(cooccurrence_query, {
                        'entity1_name': entity1['canonical_name'],
                        'entity2_name': entity2['canonical_name'],
                        'strength': strength
                    })

    async def analyze_topic_clusters(self, min_cluster_size: int = 5) -> List[Dict]:
        """Analyze topic clusters using community detection algorithms"""
        try:
            with self.driver.session() as session:
                # Get topic relationships
                topic_query = \"\"\"
                MATCH (t1:Topic)-[r:RELATED_TO]-(t2:Topic)
                RETURN t1.name as topic1, t2.name as topic2, r.strength as strength
                \"\"\"

                results = session.run(topic_query)

                # Build NetworkX graph for analysis
                G = nx.Graph()
                for record in results:
                    G.add_edge(
                        record['topic1'],
                        record['topic2'],
                        weight=record['strength']
                    )

                # Detect communities using Louvain algorithm
                import community as community_louvain
                partition = community_louvain.best_partition(G)

                # Group topics by community
                communities = {}
                for topic, community_id in partition.items():
                    if community_id not in communities:
                        communities[community_id] = []
                    communities[community_id].append(topic)

                # Filter by minimum cluster size and format results
                clusters = []
                for community_id, topics in communities.items():
                    if len(topics) >= min_cluster_size:
                        # Calculate cluster metrics
                        subgraph = G.subgraph(topics)
                        density = nx.density(subgraph)

                        clusters.append({
                            'cluster_id': community_id,
                            'topics': topics,
                            'size': len(topics),
                            'density': density,
                            'representative_topic': max(topics, key=lambda t: G.degree(t))
                        })

                return sorted(clusters, key=lambda x: x['size'], reverse=True)

        except Exception as e:
            self.logger.error(f"Topic cluster analysis failed: {e}")
            return []

    async def get_entity_influence_scores(self, limit: int = 100) -> List[Dict]:
        """Calculate entity influence scores using PageRank algorithm"""
        try:
            with self.driver.session() as session:
                # Get entity relationships
                entity_query = \"\"\"
                MATCH (e1:Entity)-[r:RELATED_TO|CO_OCCURS_WITH]-(e2:Entity)
                RETURN e1.canonical_name as entity1, e2.canonical_name as entity2,
                       coalesce(r.strength, 1.0) as weight
                \"\"\"

                results = session.run(entity_query)

                # Build NetworkX graph
                G = nx.Graph()
                for record in results:
                    G.add_edge(
                        record['entity1'],
                        record['entity2'],
                        weight=record['weight']
                    )

                # Calculate PageRank scores
                pagerank_scores = nx.pagerank(G, weight='weight')

                # Get additional entity metadata
                entity_metadata = {}
                metadata_query = \"\"\"
                MATCH (e:Entity)
                RETURN e.canonical_name as name, e.entity_type as type,
                       e.frequency_count as frequency, e.confidence_score as confidence
                \"\"\"

                metadata_results = session.run(metadata_query)
                for record in metadata_results:
                    entity_metadata[record['name']] = {
                        'entity_type': record['type'],
                        'frequency': record['frequency'],
                        'confidence': record['confidence']
                    }

                # Combine scores with metadata
                influence_scores = []
                for entity, score in pagerank_scores.items():
                    metadata = entity_metadata.get(entity, {})
                    influence_scores.append({
                        'entity': entity,
                        'influence_score': score,
                        'entity_type': metadata.get('entity_type'),
                        'frequency': metadata.get('frequency', 0),
                        'confidence': metadata.get('confidence', 0.0)
                    })

                return sorted(influence_scores, key=lambda x: x['influence_score'], reverse=True)[:limit]

        except Exception as e:
            self.logger.error(f"Entity influence calculation failed: {e}")
            return []

    def close(self):
        """Close Neo4j driver connection"""
        self.driver.close()
