import time
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import asyncio
import psutil
import threading


@dataclass
class MetricPoint:
    """Individual metric data point"""

    name: str
    value: float
    timestamp: datetime
    tags: Dict[str, str]
    metric_type: str  # counter, gauge, histogram, timer


class MetricsCollector:
    """Collect and aggregate system and application metrics"""

    def __init__(self):
        self.metrics = defaultdict(deque)
        self.counters = defaultdict(int)
        self.gauges = defaultdict(float)
        self.timers = defaultdict(list)
        self.lock = threading.Lock()

        # System monitoring
        self.system_metrics_enabled = True
        self.collection_interval = 60  # seconds

        # Start background collection
        self._start_background_collection()

    def increment_counter(self, name: str, value: int = 1, tags: Dict[str, str] = None):
        """Increment a counter metric"""
        with self.lock:
            self.counters[name] += value
            self._record_metric(name, float(value), "counter", tags or {})

    def set_gauge(self, name: str, value: float, tags: Dict[str, str] = None):
        """Set a gauge metric value"""
        with self.lock:
            self.gauges[name] = value
            self._record_metric(name, value, "gauge", tags or {})

    def record_timer(self, name: str, duration_ms: float, tags: Dict[str, str] = None):
        """Record a timing metric"""
        with self.lock:
            self.timers[name].append(duration_ms)
            # Keep only last 1000 measurements
            if len(self.timers[name]) > 1000:
                self.timers[name] = self.timers[name][-1000:]
            self._record_metric(name, duration_ms, "timer", tags or {})

    def _record_metric(self, name: str, value: float, metric_type: str, tags: Dict[str, str]):
        """Record a metric point"""
        metric_point = MetricPoint(
            name=name, value=value, timestamp=datetime.utcnow(), tags=tags, metric_type=metric_type
        )

        self.metrics[name].append(metric_point)

        # Keep only last 10000 points per metric
        if len(self.metrics[name]) > 10000:
            self.metrics[name].popleft()

    def _start_background_collection(self):
        """Start background system metrics collection"""

        def collect_system_metrics():
            while self.system_metrics_enabled:
                try:
                    # CPU metrics
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.set_gauge("system.cpu.usage_percent", cpu_percent)

                    # Memory metrics
                    memory = psutil.virtual_memory()
                    self.set_gauge("system.memory.usage_percent", memory.percent)
                    self.set_gauge("system.memory.available_bytes", memory.available)

                    # Disk metrics
                    disk = psutil.disk_usage("/")
                    self.set_gauge("system.disk.usage_percent", disk.percent)
                    self.set_gauge("system.disk.free_bytes", disk.free)

                    # Network metrics
                    network = psutil.net_io_counters()
                    # These are cumulative, so we calculate the delta
                    # This is a simplified approach; a more robust solution would store previous values
                    # For now, we record the total, which can be processed by a metrics backend
                    self.set_gauge("system.network.bytes_sent_total", float(network.bytes_sent))
                    self.set_gauge("system.network.bytes_recv_total", float(network.bytes_recv))

                    time.sleep(self.collection_interval)

                except Exception as e:
                    logging.error(f"System metrics collection failed: {e}")
                    time.sleep(self.collection_interval)  # Wait before retrying

        thread = threading.Thread(target=collect_system_metrics, daemon=True)
        thread.start()

    def get_metrics_summary(self, timeframe_minutes: int = 60) -> Dict[str, Any]:
        """Get metrics summary for the specified timeframe"""
        cutoff_time = datetime.utcnow() - timedelta(minutes=timeframe_minutes)

        summary = {
            "timeframe_minutes": timeframe_minutes,
            "counters": {},
            "gauges": {},
            "timers": {},
            "generated_at": datetime.utcnow().isoformat(),
        }

        with self.lock:
            # Process counters
            for name, points_deque in self.metrics.items():
                # Filter points by type and timeframe
                recent_points = [
                    p
                    for p in points_deque
                    if p.timestamp > cutoff_time and p.metric_type == "counter"
                ]
                if recent_points:
                    total_value = sum(p.value for p in recent_points)
                    count = len(recent_points)
                    rate_per_minute = (
                        (total_value / timeframe_minutes) if timeframe_minutes > 0 else 0
                    )
                    summary["counters"][name] = {
                        "total": total_value,
                        "count": count,
                        "rate_per_minute": rate_per_minute,
                    }

            # Process gauges (latest values from self.gauges for simplicity)
            # A more accurate gauge summary would iterate self.metrics like counters
            # but for gauges, usually the last reported value is of most interest.
            for name, value in self.gauges.items():
                # Attempt to find the most recent timestamp for this gauge from self.metrics
                last_updated_timestamp = datetime.utcnow()  # Default to now
                if name in self.metrics:
                    gauge_points = [p for p in self.metrics[name] if p.metric_type == "gauge"]
                    if gauge_points:
                        last_updated_timestamp = gauge_points[-1].timestamp

                summary["gauges"][name] = {
                    "current_value": value,
                    "last_updated": last_updated_timestamp.isoformat(),
                }

            # Process timers
            for (
                name,
                durations_deque,
            ) in self.metrics.items():  # Iterate self.metrics for timer points
                recent_durations = [
                    p.value
                    for p in durations_deque
                    if p.timestamp > cutoff_time and p.metric_type == "timer"
                ]
                if recent_durations:  # Check if there are any recent durations
                    # If self.timers[name] was used directly, it might not be time-filtered.
                    # The provided code uses self.timers[name][-100:], which is not time-filtered.
                    # Using time-filtered points from self.metrics for summary:
                    summary["timers"][name] = {
                        "avg_ms": (
                            sum(recent_durations) / len(recent_durations) if recent_durations else 0
                        ),
                        "min_ms": min(recent_durations) if recent_durations else 0,
                        "max_ms": max(recent_durations) if recent_durations else 0,
                        "count": len(recent_durations),
                    }
        return summary


# Application performance monitoring
class PerformanceMonitor:
    """Monitor application performance and detect anomalies"""

    def __init__(self, metrics_collector: MetricsCollector):
        self.metrics_collector = metrics_collector  # Renamed for clarity
        self.alert_thresholds = {
            "response_time_ms": 5000,  # 5 seconds
            "error_rate_percent": 5.0,  # 5%
            "system.cpu.usage_percent": 80.0,
            "system.memory.usage_percent": 85.0,
            "system.disk.usage_percent": 90.0,
        }

        self.alerts_sent = defaultdict(datetime)
        self.alert_cooldown_minutes = 15

    async def check_performance_alerts(self):
        """Check for performance issues and send alerts"""
        alerts = []

        # Get recent metrics summary
        summary = self.metrics_collector.get_metrics_summary(
            timeframe_minutes=5
        )  # Use the collector instance

        # Check response time alerts from timers
        for timer_name, timer_data in summary.get("timers", {}).items():
            # Assuming timer names like "api.endpoint.response_time"
            if (
                "response_time" in timer_name
                and timer_data.get("avg_ms", 0) > self.alert_thresholds["response_time_ms"]
            ):
                alerts.append(
                    {
                        "type": "performance",
                        "severity": "warning",
                        "message": f"High response time: {timer_data['avg_ms']:.2f}ms for {timer_name}",
                        "metric": timer_name,
                        "value": timer_data["avg_ms"],
                        "threshold": self.alert_thresholds["response_time_ms"],
                    }
                )

        # Check system resource alerts from gauges
        for gauge_name, gauge_data in summary.get("gauges", {}).items():
            # Gauge names like "system.cpu.usage_percent"
            if gauge_name in self.alert_thresholds:  # Direct match for system metrics
                if gauge_data.get("current_value", 0) > self.alert_thresholds[gauge_name]:
                    alerts.append(
                        {
                            "type": "resource",
                            "severity": (
                                "critical" if gauge_data["current_value"] > 95 else "warning"
                            ),
                            "message": f"High {gauge_name}: {gauge_data['current_value']:.2f}%",
                            "metric": gauge_name,
                            "value": gauge_data["current_value"],
                            "threshold": self.alert_thresholds[gauge_name],
                        }
                    )

        # Placeholder for error rate alerts (requires error counter metrics)
        # Example:
        # error_counters = summary.get("counters", {}).get("api.errors.total", {})
        # request_counters = summary.get("counters", {}).get("api.requests.total", {})
        # if error_counters and request_counters and request_counters.get("count", 0) > 0:
        #     error_rate = (error_counters.get("total", 0) / request_counters.get("total", 0)) * 100
        #     if error_rate > self.alert_thresholds["error_rate_percent"]:
        #         alerts.append(...)

        # Send alerts (with cooldown)
        for alert in alerts:
            await self._send_alert_if_needed(alert)

        if alerts:  # Log if any alerts were generated
            logging.info(f"Performance check generated {len(alerts)} alerts.")

        return alerts

    async def _send_alert_if_needed(self, alert: Dict[str, Any]):
        """Send alert if cooldown period has passed"""
        alert_key = f"{alert['type']}_{alert['metric']}"
        last_sent = self.alerts_sent.get(alert_key)

        if not last_sent or (datetime.utcnow() - last_sent).total_seconds() > (
            self.alert_cooldown_minutes * 60
        ):
            await self._send_alert(alert)
            self.alerts_sent[alert_key] = datetime.utcnow()
        else:
            logging.info(f"Alert for {alert_key} is in cooldown. Last sent: {last_sent}")

    async def _send_alert(self, alert: Dict[str, Any]):
        """Send alert notification (implement with your preferred alerting system)"""
        # This would integrate with Slack, PagerDuty, email, etc.
        logging.warning(
            f"ALERT: {alert['message']} (Metric: {alert['metric']}, Value: {alert.get('value', 'N/A')}, Threshold: {alert.get('threshold', 'N/A')})"
        )

        # Example: Send to monitoring dashboard or external service
        # await send_to_slack(alert)
        # await send_to_pagerduty(alert)


# Global metrics instance
metrics_collector = MetricsCollector()
performance_monitor = PerformanceMonitor(metrics_collector)

# Example usage (optional, for testing)
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)

    # Simulate some metrics
    metrics_collector.increment_counter("app.requests.total", tags={"endpoint": "/test"})
    metrics_collector.set_gauge("app.users.active", 150)
    metrics_collector.record_timer(
        "app.db.query_duration_ms", 120.5, tags={"query": "select_users"}
    )

    # Simulate high CPU usage for alert
    metrics_collector.set_gauge("system.cpu.usage_percent", 85.0)
    metrics_collector.set_gauge("system.memory.usage_percent", 70.0)

    async def run_perf_check():
        await performance_monitor.check_performance_alerts()

    # Run performance check after a delay to allow system metrics to be collected
    async def main():
        logging.info("Starting example monitoring...")
        # Allow some time for initial system metrics collection
        await asyncio.sleep(5)  # psutil interval is 1s, collection_interval is 60s
        # so direct system metrics might not be there immediately.
        # For testing, one might reduce collection_interval or manually set.

        # Manually trigger a high response time for testing alert
        metrics_collector.record_timer(
            "api.test.response_time", 6000, tags={"endpoint": "/critical"}
        )

        await run_perf_check()

        # Print summary
        summary = metrics_collector.get_metrics_summary(timeframe_minutes=1)
        import json

        logging.info(f"Metrics Summary: {json.dumps(summary, indent=2)}")

    asyncio.run(main())
