import spacy
from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
import asyncio
from typing import Dict, List, Optional
import json
from datetime import datetime


class ContentAnalyzer:
    """Advanced NLP content analysis service"""

    def __init__(self):
        # Load models
        self.nlp = spacy.load("en_core_web_lg")

        # Sentiment analysis
        self.sentiment_analyzer = pipeline(
            "sentiment-analysis", model="cardiffnlp/twitter-roberta-base-sentiment-latest"
        )

        # Topic classification
        self.topic_classifier = pipeline(
            "zero-shot-classification", model="facebook/bart-large-mnli"
        )

        # Language detection
        self.language_detector = pipeline(
            "text-classification", model="papluca/xlm-roberta-base-language-detection"
        )

        # Predefined topic categories
        self.topic_categories = [
            "Technology",
            "Business",
            "Science",
            "Health",
            "Politics",
            "Entertainment",
            "Sports",
            "Education",
            "Travel",
            "Food",
            "Fashion",
            "Finance",
            "Real Estate",
            "Automotive",
            "Gaming",
            "Art",
            "Music",
            "Literature",
            "News",
            "Opinion",
            "Tutorial",
            "Product Review",
            "Academic",
            "Legal",
            "Medical",
            "Research",
        ]

    async def analyze_content(self, page_data: Dict) -> Dict:
        """Perform comprehensive NLP analysis on page content"""
        content_text = page_data.get("content_text", "")

        if not content_text or len(content_text.strip()) < 50:
            return {"error": "Insufficient content for analysis"}

        start_time = datetime.now()

        # Run analyses in parallel
        tasks = [
            self._detect_language(content_text),
            self._classify_topics(content_text),
            self._extract_entities(content_text),
            self._analyze_sentiment(content_text),
            self._extract_keywords(content_text),
            self._analyze_readability(content_text),
            self._analyze_structure(content_text),
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        end_time = datetime.now()
        processing_time = int((end_time - start_time).total_seconds() * 1000)

        # Combine results
        analysis = {
            "language_analysis": (
                results[0] if not isinstance(results[0], Exception) else {"error": str(results[0])}
            ),
            "topic_analysis": (
                results[1] if not isinstance(results[1], Exception) else {"error": str(results[1])}
            ),
            "entity_analysis": (
                results[2] if not isinstance(results[2], Exception) else {"error": str(results[2])}
            ),
            "sentiment_analysis": (
                results[3] if not isinstance(results[3], Exception) else {"error": str(results[3])}
            ),
            "keyword_analysis": (
                results[4] if not isinstance(results[4], Exception) else {"error": str(results[4])}
            ),
            "readability_analysis": (
                results[5] if not isinstance(results[5], Exception) else {"error": str(results[5])}
            ),
            "structure_analysis": (
                results[6] if not isinstance(results[6], Exception) else {"error": str(results[6])}
            ),
            "processing_time_ms": processing_time,
            "model_version": "v1.0",
            "processed_at": datetime.utcnow().isoformat(),
        }

        return analysis

    async def _detect_language(self, text: str) -> Dict:
        """Detect content language"""
        try:
            # Use first 512 characters for language detection
            sample_text = text[:512]
            result = self.language_detector(sample_text)

            return {"detected_language": result[0]["label"], "confidence": result[0]["score"]}
        except Exception as e:
            return {"error": str(e)}

    async def _classify_topics(self, text: str) -> Dict:
        """Classify content topics using zero-shot classification"""
        try:
            # Use first 1000 characters for topic classification
            sample_text = text[:1000]
            result = self.topic_classifier(sample_text, self.topic_categories)

            # Create hierarchical topic structure
            primary_topic = result["labels"][0]
            topic_scores = dict(zip(result["labels"], result["scores"]))

            return {
                "primary_topic": primary_topic,
                "topic_confidence": result["scores"][0],
                "all_topics": topic_scores,
                "topic_hierarchy": self._build_topic_hierarchy(topic_scores),
            }
        except Exception as e:
            return {"error": str(e)}

    async def _extract_entities(self, text: str) -> Dict:
        """Extract named entities using spaCy"""
        try:
            doc = self.nlp(text[:5000])  # Limit to first 5000 chars

            entities = {}
            for ent in doc.ents:
                entity_type = ent.label_
                entity_text = ent.text.strip()

                if entity_type not in entities:
                    entities[entity_type] = []

                if entity_text not in entities[entity_type]:
                    entities[entity_type].append(entity_text)

            return {
                "entities": entities,
                "entity_count": sum(len(ents) for ents in entities.values()),
            }
        except Exception as e:
            return {"error": str(e)}

    async def _analyze_sentiment(self, text: str) -> Dict:
        """Analyze sentiment and emotions"""
        try:
            # Use first 512 characters for sentiment analysis
            sample_text = text[:512]
            result = self.sentiment_analyzer(sample_text)

            # Convert to polarity score (-1 to 1)
            label = result[0]["label"].lower()
            score = result[0]["score"]

            if "positive" in label:
                polarity = score
            elif "negative" in label:
                polarity = -score
            else:
                polarity = 0.0

            return {"sentiment_polarity": polarity, "sentiment_label": label, "confidence": score}
        except Exception as e:
            return {"error": str(e)}

    async def _extract_keywords(self, text: str) -> Dict:
        """Extract keywords and key phrases"""
        try:
            doc = self.nlp(text[:3000])  # Limit to first 3000 chars

            # Extract keywords based on POS tags and frequency
            keywords = {}
            for token in doc:
                if (
                    token.pos_ in ["NOUN", "ADJ", "VERB"]
                    and not token.is_stop
                    and not token.is_punct
                    and len(token.text) > 2
                ):

                    lemma = token.lemma_.lower()
                    keywords[lemma] = keywords.get(lemma, 0) + 1

            # Sort by frequency and get top keywords
            sorted_keywords = sorted(keywords.items(), key=lambda x: x[1], reverse=True)
            top_keywords = [
                {
                    "keyword": kw,
                    "frequency": freq,
                    "score": freq / len(doc) if doc else 0,
                }  # Added check for empty doc
                for kw, freq in sorted_keywords[:20]
            ]

            return {"keywords": top_keywords, "total_keywords": len(keywords)}
        except Exception as e:
            return {"error": str(e)}

    async def _analyze_readability(self, text: str) -> Dict:
        """Analyze text readability and complexity"""
        try:
            sentences = [s for s in text.split(".") if s.strip()]  # Ensure non-empty sentences
            words = text.split()

            if not sentences or not words:  # Check if sentences or words list is empty
                return {
                    "readability_score": 0.0,
                    "avg_sentence_length": 0.0,
                    "avg_word_length": 0.0,
                }

            # Simple readability metrics
            avg_sentence_length = len(words) / len(sentences)
            avg_word_length = (
                sum(len(word) for word in words) / len(words) if words else 0
            )  # Check for empty words list

            # Flesch Reading Ease approximation
            # Ensure avg_word_length is not zero to avoid division by zero if all words are empty strings (though unlikely with split())
            readability_score = (
                206.835
                - (1.015 * avg_sentence_length)
                - (84.6 * avg_word_length / 4.7 if avg_word_length > 0 else 0)
            )
            readability_score = max(0, min(100, readability_score)) / 100  # Normalize to 0-1

            return {
                "readability_score": readability_score,
                "avg_sentence_length": avg_sentence_length,
                "avg_word_length": avg_word_length,
            }
        except Exception as e:
            return {"error": str(e)}

    async def _analyze_structure(self, text: str) -> Dict:
        """Analyze content structure"""
        try:
            paragraphs = [p.strip() for p in text.split("\n\n") if p.strip()]
            sentences = [s.strip() for s in text.split(".") if s.strip()]
            words = text.split()

            return {
                "paragraph_count": len(paragraphs),
                "sentence_count": len(sentences),
                "word_count": len(words),
                "avg_paragraph_length": len(words) / len(paragraphs) if paragraphs else 0,
                "avg_sentence_length": len(words) / len(sentences) if sentences else 0,
            }
        except Exception as e:
            return {"error": str(e)}

    def _build_topic_hierarchy(self, topic_scores: Dict) -> Dict:
        """Build hierarchical topic structure"""
        # Simplified hierarchy - in production, use a proper taxonomy
        if not topic_scores:  # Handle empty topic_scores
            return {"primary": (None, 0.0), "secondary": [], "confidence_threshold": 0.5}
        hierarchy = {
            "primary": max(topic_scores.items(), key=lambda x: x[1]),
            "secondary": sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)[1:3],
            "confidence_threshold": 0.5,
        }
        return hierarchy
