import spacy
from spacy import displacy
from collections import defaultdict, Counter
import re
from typing import Dict, List, Tuple, Set
from datetime import datetime
import logging


class EntityExtractor:
    """Advanced entity extraction and relationship mapping"""

    def __init__(self, session):
        self.session = session
        self.nlp = spacy.load("en_core_web_lg")

        # Add custom entity patterns if needed
        self.entity_cache = {}  # Cache for entity normalization

        # Entity type mapping
        self.entity_type_mapping = {
            "PERSON": "PERSON",
            "ORG": "ORGANIZATION",
            "GPE": "LOCATION",
            "PRODUCT": "PRODUCT",
            "EVENT": "EVENT",
            "WORK_OF_ART": "CREATIVE_WORK",
            "LAW": "LEGAL_DOCUMENT",
            "LANGUAGE": "LANGUAGE",
            "DATE": "DATE",
            "TIME": "TIME",
            "PERCENT": "PERCENTAGE",
            "MONEY": "MONETARY_VALUE",
            "QUANTITY": "QUANTITY",
            "ORDINAL": "ORDINAL",
            "CARDINAL": "CARDINAL",
        }

    async def extract_entities(self, page_data: Dict) -> Dict:
        """Extract entities and relationships from page content"""
        content_text = page_data.get("content_text", "")
        page_id = page_data.get("page_id")

        if not content_text or len(content_text.strip()) < 50:
            return {
                "error": "Insufficient content for entity extraction",
                "processing_successful": False,
            }

        try:
            # Process text with spaCy
            doc = self.nlp(content_text[:10000])  # Limit to first 10k chars

            # Extract entities
            entities = self._extract_entities_from_doc(doc)

            # Find entity relationships
            relationships = self._find_entity_relationships(doc, entities)

            # Store entities and relationships
            stored_entities = await self._store_entities(entities, page_id)
            stored_relationships = await self._store_relationships(relationships)

            return {
                "entities_found": len(entities),
                "relationships_found": len(relationships),
                "entities": stored_entities,
                "relationships": stored_relationships,
                "processing_successful": True,
            }

        except Exception as e:
            logging.error(f"Entity extraction failed for page {page_id}: {e}")
            return {"error": str(e), "processing_successful": False}

    def _extract_entities_from_doc(self, doc) -> List[Dict]:
        """Extract entities with context from spaCy doc"""
        entities = []

        for ent in doc.ents:
            # Skip very short or common entities
            if len(ent.text.strip()) < 2 or ent.text.lower() in ["the", "and", "or"]:
                continue

            # Get context around entity
            start_char = max(0, ent.start_char - 100)
            end_char = min(len(doc.text), ent.end_char + 100)
            context_before = doc.text[start_char : ent.start_char].strip()
            context_after = doc.text[ent.end_char : end_char].strip()

            entity_data = {
                "text": ent.text.strip(),
                "label": self.entity_type_mapping.get(ent.label_, ent.label_),
                "start_char": ent.start_char,
                "end_char": ent.end_char,
                "context_before": context_before[-100:],  # Last 100 chars
                "context_after": context_after[:100],  # First 100 chars
                "confidence": getattr(
                    ent, "sentiment", 0.8
                ),  # Default confidence, spacy v3 uses 'sentiment'
                "canonical_name": self._normalize_entity_name(ent.text, ent.label_),
            }

            entities.append(entity_data)

        return entities

    def _normalize_entity_name(self, entity_text: str, entity_type: str) -> str:
        """Normalize entity names for deduplication"""
        # Basic normalization - can be enhanced with external APIs
        normalized = entity_text.strip().title()

        # Remove common prefixes/suffixes for organizations
        if entity_type in ["ORG", "ORGANIZATION"]:
            normalized = re.sub(
                r"\b(Inc|LLC|Corp|Ltd|Co)\b\.?$", "", normalized, flags=re.IGNORECASE
            )
            normalized = normalized.strip()

        # Remove titles for persons
        if entity_type == "PERSON":
            normalized = re.sub(
                r"^(Mr|Mrs|Ms|Dr|Prof|Sir|Dame)\.?\s+", "", normalized, flags=re.IGNORECASE
            )
            normalized = normalized.strip()

        return normalized.strip()

    def _find_entity_relationships(self, doc, entities: List[Dict]) -> List[Dict]:
        """Find relationships between entities based on co-occurrence"""
        relationships = []

        # Group entities by sentence for co-occurrence analysis
        sentence_entities_map = defaultdict(list)  # Renamed to avoid conflict

        for sent in doc.sents:
            sent_entities_list = []
            for entity in entities:
                if sent.start_char <= entity["start_char"] < sent.end_char:
                    sent_entities_list.append(entity)

            if len(sent_entities_list) >= 2:
                sentence_entities_map[sent.text] = sent_entities_list

        # Find co-occurrences within sentences
        for sentence, sent_entities_list_val in sentence_entities_map.items():
            for i, entity1 in enumerate(sent_entities_list_val):
                for entity2 in sent_entities_list_val[i + 1 :]:
                    relationship = {
                        "entity1": entity1["canonical_name"],
                        "entity1_type": entity1["label"],
                        "entity2": entity2["canonical_name"],
                        "entity2_type": entity2["label"],
                        "relationship_type": "co_occurrence",
                        "context": sentence[:500],  # First 500 chars of sentence
                        "confidence": min(entity1["confidence"], entity2["confidence"]),
                    }
                    relationships.append(relationship)

        return relationships

    async def _store_entities(self, entities: List[Dict], page_id: str) -> List[Dict]:
        """Store entities in database with deduplication"""
        from models.entities import Entity, PageEntity

        stored_entities = []

        for entity_data in entities:
            # Check if entity already exists
            existing_entity = (
                self.session.query(Entity)
                .filter_by(
                    canonical_name=entity_data["canonical_name"], entity_type=entity_data["label"]
                )
                .first()
            )

            if existing_entity:
                # Update frequency and last mentioned
                existing_entity.frequency_count = (
                    existing_entity.frequency_count or 0
                ) + 1  # Handle None case
                existing_entity.last_mentioned = datetime.utcnow()
                entity_model = existing_entity  # Renamed to avoid conflict
            else:
                # Create new entity
                entity_model = Entity(  # Renamed to avoid conflict
                    name=entity_data["text"],
                    entity_type=entity_data["label"],
                    canonical_name=entity_data["canonical_name"],
                    confidence_score=entity_data["confidence"],
                    frequency_count=1,
                )
                self.session.add(entity_model)
                self.session.flush()  # Get the ID

            # Check if page-entity relationship already exists
            existing_page_entity = (
                self.session.query(PageEntity)
                .filter_by(
                    page_id=page_id,
                    entity_id=entity_model.id,
                    position_in_text=entity_data["start_char"],
                )
                .first()
            )

            if not existing_page_entity:
                # Create page-entity relationship
                page_entity = PageEntity(
                    page_id=page_id,
                    entity_id=entity_model.id,  # Use renamed variable
                    mention_text=entity_data["text"],
                    context_before=entity_data["context_before"],
                    context_after=entity_data["context_after"],
                    position_in_text=entity_data["start_char"],
                    confidence_score=entity_data["confidence"],
                    mention_type="direct",
                )
                self.session.add(page_entity)

            stored_entities.append(
                {
                    "id": str(entity_model.id),  # Use renamed variable
                    "name": entity_model.name,  # Use renamed variable
                    "type": entity_model.entity_type,  # Use renamed variable
                    "canonical_name": entity_model.canonical_name,  # Use renamed variable
                }
            )

        if entities:  # Only commit if there were entities to process
            self.session.commit()
        return stored_entities

    async def _store_relationships(self, relationships: List[Dict]) -> List[Dict]:
        """Store entity relationships with deduplication"""
        from models.entities import Entity, EntityRelationship

        stored_relationships = []

        if not relationships:  # Early exit if no relationships to process
            return stored_relationships

        for rel_data in relationships:
            # Find entities
            entity1 = (
                self.session.query(Entity)
                .filter_by(canonical_name=rel_data["entity1"], entity_type=rel_data["entity1_type"])
                .first()
            )

            entity2 = (
                self.session.query(Entity)
                .filter_by(canonical_name=rel_data["entity2"], entity_type=rel_data["entity2_type"])
                .first()
            )

            if not entity1 or not entity2:
                continue

            # Check if relationship already exists
            existing_rel = (
                self.session.query(EntityRelationship)
                .filter_by(
                    entity1_id=entity1.id,
                    entity2_id=entity2.id,
                    relationship_type=rel_data["relationship_type"],
                )
                .first()
            )

            if existing_rel:
                # Update co-occurrence count
                existing_rel.co_occurrence_count = (
                    existing_rel.co_occurrence_count or 0
                ) + 1  # Handle None case
                existing_rel.last_seen = datetime.utcnow()

                # Add context to examples (limit to 10 examples)
                examples = existing_rel.example_contexts or []
                if len(examples) < 10:
                    examples.append(rel_data["context"])
                    existing_rel.example_contexts = examples

                relationship_model = existing_rel  # Renamed
            else:
                # Create new relationship
                relationship_model = EntityRelationship(  # Renamed
                    entity1_id=entity1.id,
                    entity2_id=entity2.id,
                    relationship_type=rel_data["relationship_type"],
                    co_occurrence_count=1,
                    relationship_strength=rel_data["confidence"],
                    example_contexts=[rel_data["context"]],
                )
                self.session.add(relationship_model)
                self.session.flush()

            stored_relationships.append(
                {
                    "id": str(relationship_model.id),
                    "entity1": rel_data["entity1"],
                    "entity2": rel_data["entity2"],
                    "type": rel_data["relationship_type"],
                    "strength": relationship_model.relationship_strength,
                    "context": rel_data.get("context"),  # Add context here
                }
            )

        if relationships:  # Only commit if there were relationships to process
            self.session.commit()
        return stored_relationships
