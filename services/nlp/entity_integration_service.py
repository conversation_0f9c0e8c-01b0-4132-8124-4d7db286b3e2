# services/nlp/entity_integration_service.py
import asyncio
import logging
from typing import Dict, List, Optional
from config.database import get_db_session
from services.nlp.entity_extractor import EntityExtractor
from sqlalchemy import text

logger = logging.getLogger(__name__)


class EntityIntegrationService:
    """Service for integrating entity extraction with the web crawler pipeline"""

    def __init__(self):
        self.session = None
        self.entity_extractor = None

    def _get_session(self):
        """Get database session"""
        if not self.session:
            self.session = next(get_db_session())
        return self.session

    def _get_entity_extractor(self):
        """Get entity extractor instance"""
        if not self.entity_extractor:
            session = self._get_session()
            self.entity_extractor = EntityExtractor(session)
        return self.entity_extractor

    async def process_page_entities(self, page_id: str, page_data: Dict) -> Optional[Dict]:
        """
        Process a single page for entity extraction and storage

        Args:
            page_id: UUID of the page to process
            page_data: Dictionary containing page content and metadata

        Returns:
            Dictionary with entity extraction results or None if failed
        """
        try:
            extractor = self._get_entity_extractor()

            # Prepare page data for entity extraction
            extraction_data = {
                "page_id": page_id,
                "content_text": page_data.get("content_text", ""),
                "title": page_data.get("title", ""),
                "url": page_data.get("url", ""),
            }

            logger.info(f"Starting entity extraction for page {page_id}")
            result = await extractor.extract_entities(extraction_data)

            if result.get("processing_successful"):
                logger.info(
                    f"Successfully extracted {result.get('entities_found', 0)} entities and {result.get('relationships_found', 0)} relationships for page {page_id}"
                )
                return result
            else:
                logger.warning(
                    f"Entity extraction failed for page {page_id}: {result.get('error', 'Unknown error')}"
                )
                return None

        except Exception as e:
            logger.error(f"Entity processing failed for page {page_id}: {e}")
            return None

    async def analyze_existing_pages(self, limit: int = 10) -> Dict:
        """
        Analyze existing pages that don't have entity extraction yet

        Args:
            limit: Maximum number of pages to process

        Returns:
            Dictionary with processing statistics
        """
        session = self._get_session()

        try:
            # Find pages without entity extraction
            result = session.execute(
                text(
                    """
                SELECT p.id, p.title, p.content_text, p.url
                FROM pages p
                LEFT JOIN page_entities pe ON p.id = pe.page_id
                WHERE pe.page_id IS NULL 
                AND p.content_text IS NOT NULL 
                AND LENGTH(p.content_text) > 100
                LIMIT :limit
            """
                ),
                {"limit": limit},
            )

            pages = result.fetchall()

            if not pages:
                logger.info("No pages found that need entity extraction")
                return {"processed": 0, "failed": 0, "total": 0}

            logger.info(f"Found {len(pages)} pages for entity extraction")

            processed = 0
            failed = 0

            for page_row in pages:
                page_id, title, content_text, url = page_row

                page_data = {"content_text": content_text, "title": title or "", "url": url or ""}

                result = await self.process_page_entities(str(page_id), page_data)

                if result:
                    processed += 1
                    logger.info(f"✅ Processed page: {title[:50]}...")
                else:
                    failed += 1
                    logger.warning(f"❌ Failed to process page: {title[:50]}...")

                # Small delay to prevent overwhelming the system
                await asyncio.sleep(0.1)

            return {"processed": processed, "failed": failed, "total": len(pages)}

        except Exception as e:
            logger.error(f"Error analyzing existing pages: {e}")
            return {"processed": 0, "failed": 0, "total": 0, "error": str(e)}

    async def get_entity_statistics(self) -> Dict:
        """Get statistics about extracted entities"""
        session = self._get_session()

        try:
            # Entity counts by type
            entity_types = session.execute(
                text(
                    """
                SELECT entity_type, COUNT(*) as count
                FROM entities
                GROUP BY entity_type
                ORDER BY count DESC
            """
                )
            ).fetchall()

            # Total counts
            total_entities = session.execute(text("SELECT COUNT(*) FROM entities")).fetchone()[0]
            total_relationships = session.execute(
                text("SELECT COUNT(*) FROM entity_relationships")
            ).fetchone()[0]
            total_page_entities = session.execute(
                text("SELECT COUNT(*) FROM page_entities")
            ).fetchone()[0]

            # Top entities by frequency
            top_entities = session.execute(
                text(
                    """
                SELECT name, entity_type, frequency_count
                FROM entities
                ORDER BY frequency_count DESC
                LIMIT 10
            """
                )
            ).fetchall()

            return {
                "total_entities": total_entities,
                "total_relationships": total_relationships,
                "total_page_entities": total_page_entities,
                "entity_types": [{"type": row[0], "count": row[1]} for row in entity_types],
                "top_entities": [
                    {"name": row[0], "type": row[1], "frequency": row[2]} for row in top_entities
                ],
            }

        except Exception as e:
            logger.error(f"Error getting entity statistics: {e}")
            return {"error": str(e)}

    async def find_entity_by_name(self, entity_name: str, entity_type: str = None) -> List[Dict]:
        """Find entities by name and optionally type"""
        session = self._get_session()

        try:
            query = """
                SELECT e.id, e.name, e.entity_type, e.canonical_name, e.frequency_count,
                       COUNT(pe.id) as page_mentions
                FROM entities e
                LEFT JOIN page_entities pe ON e.id = pe.entity_id
                WHERE LOWER(e.name) LIKE LOWER(:name)
            """
            params = {"name": f"%{entity_name}%"}

            if entity_type:
                query += " AND e.entity_type = :entity_type"
                params["entity_type"] = entity_type

            query += " GROUP BY e.id, e.name, e.entity_type, e.canonical_name, e.frequency_count ORDER BY e.frequency_count DESC"

            result = session.execute(text(query), params)

            entities = []
            for row in result:
                entities.append(
                    {
                        "id": str(row[0]),
                        "name": row[1],
                        "type": row[2],
                        "canonical_name": row[3],
                        "frequency": row[4],
                        "page_mentions": row[5],
                    }
                )

            return entities

        except Exception as e:
            logger.error(f"Error finding entity by name: {e}")
            return []

    def close(self):
        """Close database session"""
        if self.session:
            self.session.close()
            self.session = None
            self.entity_extractor = None


# Test function
async def test_entity_integration():
    """Test the entity integration service"""
    print("🧠 Testing Entity Integration Service")
    print("=" * 50)

    service = EntityIntegrationService()

    try:
        # Get statistics
        stats = await service.get_entity_statistics()
        print(f"📊 Entity Statistics:")
        print(f"   - Total entities: {stats.get('total_entities', 0)}")
        print(f"   - Total relationships: {stats.get('total_relationships', 0)}")
        print(f"   - Total page-entity links: {stats.get('total_page_entities', 0)}")

        # Show entity types
        if "entity_types" in stats:
            print(f"\n📋 Entity types:")
            for entity_type in stats["entity_types"][:5]:
                print(f"   - {entity_type['type']}: {entity_type['count']}")

        # Process more pages if available
        print(f"\n🔄 Processing additional pages...")
        result = await service.analyze_existing_pages(limit=2)
        print(f"   - Processed: {result['processed']}")
        print(f"   - Failed: {result['failed']}")
        print(f"   - Total: {result['total']}")

    finally:
        service.close()


if __name__ == "__main__":
    asyncio.run(test_entity_integration())
