# NLP Integration Service for Milestone 2.1: NLP Content Analysis Pipeline
# Integrates content scraping with NLP analysis and database storage

import asyncio
import logging
from datetime import datetime
from typing import Dict, Optional
from sqlalchemy.orm import Session
from sqlalchemy import text

from services.nlp.content_analyzer import ContentAnalyzer
from models.content_analysis import NLPAnalysis
from models.pages import Page
from config.database import get_db_session

logger = logging.getLogger(__name__)


class NLPIntegrationService:
    """Service to integrate NLP analysis with content scraping and database storage"""

    def __init__(self):
        self.content_analyzer = ContentAnalyzer()
        logger.info("NLPIntegrationService initialized with ContentAnalyzer")

    async def process_page_content(self, page_id: str, page_data: Dict) -> Optional[Dict]:
        """
        Process page content with NLP analysis and store results in database

        Args:
            page_id: UUID of the page in the database
            page_data: Dictionary containing page content and metadata

        Returns:
            Dictionary with NLP analysis results or None if failed
        """
        try:
            # Perform NLP analysis
            logger.info(f"Starting NLP analysis for page {page_id}")
            analysis_result = await self.content_analyzer.analyze_content(page_data)

            if "error" in analysis_result:
                logger.warning(
                    f"NLP analysis failed for page {page_id}: {analysis_result['error']}"
                )
                return None

            # Store results in database
            stored_result = await self._store_nlp_results(page_id, analysis_result)

            if stored_result:
                logger.info(f"Successfully processed and stored NLP analysis for page {page_id}")
                return analysis_result
            else:
                logger.error(f"Failed to store NLP analysis for page {page_id}")
                return None

        except Exception as e:
            logger.error(f"Error processing page content for {page_id}: {str(e)}")
            return None

    async def _store_nlp_results(self, page_id: str, analysis_result: Dict) -> bool:
        """Store NLP analysis results in the database"""
        try:
            session = next(get_db_session())

            # Extract analysis components
            language_analysis = analysis_result.get("language_analysis", {})
            topic_analysis = analysis_result.get("topic_analysis", {})
            entity_analysis = analysis_result.get("entity_analysis", {})
            sentiment_analysis = analysis_result.get("sentiment_analysis", {})
            keyword_analysis = analysis_result.get("keyword_analysis", {})
            readability_analysis = analysis_result.get("readability_analysis", {})
            structure_analysis = analysis_result.get("structure_analysis", {})

            # Create NLP analysis record
            nlp_record = NLPAnalysis(
                page_id=page_id,
                # Language detection
                detected_language=language_analysis.get("detected_language"),
                language_confidence=language_analysis.get("confidence"),
                # Topic classification
                primary_topic=topic_analysis.get("primary_topic"),
                topic_confidence=topic_analysis.get("topic_confidence"),
                topic_hierarchy=topic_analysis.get("topic_hierarchy"),
                # Named entity recognition
                entities=entity_analysis.get("entities"),
                entity_count=entity_analysis.get("entity_count"),
                # Keyword extraction
                keywords=keyword_analysis.get("keywords"),
                key_phrases=keyword_analysis.get("key_phrases"),
                # Sentiment analysis
                sentiment_polarity=sentiment_analysis.get("sentiment_polarity"),
                sentiment_subjectivity=sentiment_analysis.get("sentiment_subjectivity"),
                emotion_scores=sentiment_analysis.get("emotion_scores"),
                # Content quality metrics
                readability_score=readability_analysis.get("readability_score"),
                grammar_score=readability_analysis.get("grammar_score"),
                coherence_score=readability_analysis.get("coherence_score"),
                # Content structure analysis
                paragraph_count=structure_analysis.get("paragraph_count"),
                sentence_count=structure_analysis.get("sentence_count"),
                avg_sentence_length=structure_analysis.get("avg_sentence_length"),
                complexity_score=structure_analysis.get("complexity_score"),
                # Processing metadata
                model_version=analysis_result.get("model_version"),
                processing_time_ms=analysis_result.get("processing_time_ms"),
                processed_at=(
                    datetime.fromisoformat(
                        analysis_result.get("processed_at").replace("Z", "+00:00")
                    )
                    if analysis_result.get("processed_at")
                    else datetime.now()
                ),
            )

            # Add to session and commit
            session.add(nlp_record)
            session.commit()

            logger.info(f"Successfully stored NLP analysis for page {page_id}")
            return True

        except Exception as e:
            logger.error(f"Error storing NLP results for page {page_id}: {str(e)}")
            if "session" in locals():
                session.rollback()
            return False
        finally:
            if "session" in locals():
                session.close()

    async def get_nlp_analysis(self, page_id: str) -> Optional[Dict]:
        """Retrieve NLP analysis results for a page"""
        try:
            session = next(get_db_session())

            # Query NLP analysis
            nlp_record = session.query(NLPAnalysis).filter(NLPAnalysis.page_id == page_id).first()

            if not nlp_record:
                return None

            # Convert to dictionary
            result = {
                "page_id": str(nlp_record.page_id),
                "detected_language": nlp_record.detected_language,
                "language_confidence": nlp_record.language_confidence,
                "primary_topic": nlp_record.primary_topic,
                "topic_confidence": nlp_record.topic_confidence,
                "topic_hierarchy": nlp_record.topic_hierarchy,
                "entities": nlp_record.entities,
                "entity_count": nlp_record.entity_count,
                "keywords": nlp_record.keywords,
                "key_phrases": nlp_record.key_phrases,
                "sentiment_polarity": nlp_record.sentiment_polarity,
                "sentiment_subjectivity": nlp_record.sentiment_subjectivity,
                "emotion_scores": nlp_record.emotion_scores,
                "readability_score": nlp_record.readability_score,
                "grammar_score": nlp_record.grammar_score,
                "coherence_score": nlp_record.coherence_score,
                "paragraph_count": nlp_record.paragraph_count,
                "sentence_count": nlp_record.sentence_count,
                "avg_sentence_length": nlp_record.avg_sentence_length,
                "complexity_score": nlp_record.complexity_score,
                "model_version": nlp_record.model_version,
                "processing_time_ms": nlp_record.processing_time_ms,
                "processed_at": (
                    nlp_record.processed_at.isoformat() if nlp_record.processed_at else None
                ),
                "created_at": nlp_record.created_at.isoformat() if nlp_record.created_at else None,
            }

            return result

        except Exception as e:
            logger.error(f"Error retrieving NLP analysis for page {page_id}: {str(e)}")
            return None
        finally:
            if "session" in locals():
                session.close()

    async def analyze_existing_pages(self, limit: int = 10) -> Dict:
        """
        Analyze existing pages that don't have NLP analysis yet

        Args:
            limit: Maximum number of pages to process

        Returns:
            Dictionary with processing statistics
        """
        try:
            session = next(get_db_session())

            # Find pages without NLP analysis
            pages_query = (
                session.query(Page)
                .outerjoin(NLPAnalysis)
                .filter(NLPAnalysis.id.is_(None))
                .limit(limit)
            )

            pages = pages_query.all()

            if not pages:
                logger.info("No pages found that need NLP analysis")
                return {"processed": 0, "failed": 0, "total": 0}

            logger.info(f"Found {len(pages)} pages to analyze")

            processed = 0
            failed = 0

            for page in pages:
                try:
                    # Prepare page data for analysis
                    page_data = {
                        "content_text": page.content_text or "",
                        "title": page.title or "",
                        "url": page.url,
                        "meta_description": page.meta_description or "",
                    }

                    # Skip if insufficient content
                    if not page_data["content_text"] or len(page_data["content_text"].strip()) < 50:
                        logger.warning(f"Skipping page {page.id} - insufficient content")
                        continue

                    # Process with NLP
                    result = await self.process_page_content(str(page.id), page_data)

                    if result:
                        processed += 1
                        logger.info(f"Successfully processed page {page.id}")
                    else:
                        failed += 1
                        logger.warning(f"Failed to process page {page.id}")

                except Exception as e:
                    failed += 1
                    logger.error(f"Error processing page {page.id}: {str(e)}")

            return {"processed": processed, "failed": failed, "total": len(pages)}

        except Exception as e:
            logger.error(f"Error in analyze_existing_pages: {str(e)}")
            return {"processed": 0, "failed": 0, "total": 0, "error": str(e)}
        finally:
            if "session" in locals():
                session.close()


# Test function
async def test_nlp_integration():
    """Test the NLP integration service"""
    logging.basicConfig(level=logging.INFO)

    service = NLPIntegrationService()

    # Test with sample page data
    page_data = {
        "content_text": "This is a comprehensive test article about artificial intelligence and machine learning technologies. The field is rapidly advancing with new breakthroughs in deep learning, natural language processing, and computer vision. Companies worldwide are investing heavily in AI research and development to gain competitive advantages in their respective markets.",
        "title": "AI Technology Advances",
        "url": "https://example.com/ai-advances",
        "meta_description": "Latest advances in AI technology and machine learning",
    }

    # Test processing (using a mock page ID)
    test_page_id = "550e8400-e29b-41d4-a716-446655440000"

    print("Testing NLP integration service...")
    result = await service.process_page_content(test_page_id, page_data)

    if result:
        print("✅ NLP integration test successful")
        print(f"Language: {result.get('language_analysis', {}).get('detected_language')}")
        print(f"Topic: {result.get('topic_analysis', {}).get('primary_topic')}")
        print(f"Sentiment: {result.get('sentiment_analysis', {}).get('sentiment_polarity')}")
    else:
        print("❌ NLP integration test failed")


if __name__ == "__main__":
    asyncio.run(test_nlp_integration())
