# tests/conftest.py
import pytest
import os
import tempfile
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

# Add project root to Python path
import sys
from pathlib import Path

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models import Base
from config.database import DatabaseConfig


@pytest.fixture(scope="session")
def test_engine():
    """Create a test database engine using SQLite in memory"""
    engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False},
        poolclass=StaticPool,
        echo=False,
    )

    # Create all tables
    Base.metadata.create_all(bind=engine)

    yield engine

    # Cleanup
    Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_session(test_engine):
    """Create a test database session"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

    session = TestingSessionLocal()

    yield session

    session.rollback()
    session.close()


@pytest.fixture
def sample_site_data():
    """Sample site data for testing"""
    return {
        "domain": "example.com",
        "title": "Example Domain",
        "description": "This domain is for use in illustrative examples",
        "language": "en",
    }


@pytest.fixture
def sample_page_data():
    """Sample page data for testing"""
    return {
        "url": "https://example.com/test-page",
        "title": "Test Page",
        "html_content": "<html><body><h1>Test Page</h1><p>This is a test page.</p></body></html>",
        "text_content": "Test Page\nThis is a test page.",
        "http_status": 200,
        "content_type": "text/html",
    }
