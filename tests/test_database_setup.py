# tests/test_database_setup.py
import pytest
from models import Site, URLFrontier, Page, Link, CrawlLog


def test_site_creation(test_session, sample_site_data):
    """Test creating a site record"""
    site = Site(**sample_site_data)
    test_session.add(site)
    test_session.commit()

    # Verify site was created
    assert site.id is not None
    assert site.domain == sample_site_data["domain"]
    assert site.title == sample_site_data["title"]
    assert site.is_active is True


def test_url_frontier_creation(test_session, sample_site_data):
    """Test creating URL frontier records"""
    # Create a site first
    site = Site(**sample_site_data)
    test_session.add(site)
    test_session.commit()

    # Create URL frontier entry
    url_entry = URLFrontier(url="https://example.com/test", site_id=site.id, priority=5, depth=0)
    test_session.add(url_entry)
    test_session.commit()

    # Verify URL frontier entry
    assert url_entry.id is not None
    assert url_entry.url == "https://example.com/test"
    assert url_entry.status == "pending"
    assert url_entry.site_id == site.id


def test_page_creation(test_session, sample_site_data, sample_page_data):
    """Test creating a page record"""
    # Create a site first
    site = Site(**sample_site_data)
    test_session.add(site)
    test_session.commit()

    # Create page
    page = Page(site_id=site.id, **sample_page_data)
    page.set_content(sample_page_data["html_content"], sample_page_data["text_content"])
    test_session.add(page)
    test_session.commit()

    # Verify page was created
    assert page.id is not None
    assert page.url == sample_page_data["url"]
    assert page.content_hash is not None
    assert page.word_count > 0
    assert page.is_successful() is True


def test_link_creation(test_session, sample_site_data, sample_page_data):
    """Test creating link records"""
    # Create a site and page first
    site = Site(**sample_site_data)
    test_session.add(site)
    test_session.commit()

    page = Page(site_id=site.id, **sample_page_data)
    test_session.add(page)
    test_session.commit()

    # Create link
    link = Link(
        from_page_id=page.id,
        to_url="https://example.com/linked-page",
        anchor_text="Link to another page",
        link_type="internal",
    )
    test_session.add(link)
    test_session.commit()

    # Verify link was created
    assert link.id is not None
    assert link.from_page_id == page.id
    assert link.to_url == "https://example.com/linked-page"
    assert link.is_internal() is True


def test_crawl_log_creation(test_session, sample_site_data):
    """Test creating crawl log records"""
    # Create a site first
    site = Site(**sample_site_data)
    test_session.add(site)
    test_session.commit()

    # Create crawl log
    log_entry = CrawlLog.log_success(
        session=test_session,
        url="https://example.com/test",
        site_id=site.id,
        http_status=200,
        response_time_ms=150,
        content_length=1024,
        user_agent="WebCrawler/1.0",
    )
    test_session.commit()

    # Verify log entry
    assert log_entry.id is not None
    assert log_entry.status == "success"
    assert log_entry.http_status == 200
    assert log_entry.response_time_ms == 150


def test_relationships(test_session, sample_site_data, sample_page_data):
    """Test model relationships"""
    # Create site
    site = Site(**sample_site_data)
    test_session.add(site)
    test_session.commit()

    # Create page
    page = Page(site_id=site.id, **sample_page_data)
    test_session.add(page)
    test_session.commit()

    # Create URL frontier entry
    url_entry = URLFrontier(url="https://example.com/test", site_id=site.id)
    test_session.add(url_entry)
    test_session.commit()

    # Test relationships
    assert page.site == site
    assert site.pages[0] == page
    assert url_entry.site == site
    assert site.url_frontier[0] == url_entry
