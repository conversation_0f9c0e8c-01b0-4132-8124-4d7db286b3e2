import pytest
from services.nlp.content_analyzer import ContentAnalyzer


class TestContentAnalyzer:

    @pytest.mark.asyncio
    async def test_language_detection(self):
        """Test language detection accuracy"""
        analyzer = ContentAnalyzer()

        english_text = "This is a sample English text for testing."
        # The _detect_language method in the roadmap is private, but for testing purposes,
        # it's common to test private methods directly if they contain complex logic.
        # Alternatively, we would test this via the public analyze_content method.
        result = await analyzer._detect_language(english_text)

        assert result["detected_language"] in [
            "en",
            "english",
            "LABEL_0",
        ]  # model "papluca/xlm-roberta-base-language-detection" returns LABEL_0 for english
        assert result["confidence"] > 0.8

    @pytest.mark.asyncio
    async def test_entity_extraction(self):
        """Test named entity recognition"""
        analyzer = ContentAnalyzer()

        text = "Apple Inc. was founded by <PERSON> in Cupertino, California."
        # Similar to language detection, _extract_entities is a private method.
        result = await analyzer._extract_entities(text)

        assert "ORG" in result["entities"]
        assert "PERSON" in result["entities"]
        assert "GPE" in result["entities"]
        assert "Apple Inc." in result["entities"]["ORG"]
        assert "Steve Jobs" in result["entities"]["PERSON"]
        assert "Cupertino" in result["entities"]["GPE"]
        assert "California" in result["entities"]["GPE"]

    @pytest.mark.asyncio
    async def test_sentiment_analysis(self):
        """Test sentiment analysis accuracy"""
        analyzer = ContentAnalyzer()

        positive_text = "This is an amazing product that I absolutely love!"
        negative_text = "This is terrible and I hate it completely."

        # _analyze_sentiment is also a private method.
        pos_result = await analyzer._analyze_sentiment(positive_text)
        neg_result = await analyzer._analyze_sentiment(negative_text)

        # The model 'cardiffnlp/twitter-roberta-base-sentiment-latest' returns 'positive', 'negative', 'neutral'
        # and the ContentAnalyzer converts this to a polarity score.
        assert pos_result["sentiment_polarity"] > 0
        assert neg_result["sentiment_polarity"] < 0
        assert (
            pos_result["sentiment_label"].lower() == "positive"
        )  # or relevant positive label from model
        assert (
            neg_result["sentiment_label"].lower() == "negative"
        )  # or relevant negative label from model
